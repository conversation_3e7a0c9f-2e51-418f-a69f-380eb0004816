[package]
name = "rfstore"
version = "0.0.1"
authors = ["The TiKV Authors"]
license = "Apache-2.0"
edition = "2018"
publish = false

[features]
testexport = ["kvengine/testexport"]

[dependencies]
api_version = { path = "../../components/api_version", default-features = false }
async-trait = "0.1"
bitflags = "1.0.1"
byteorder = "1.2"
bytes = "1.0"
cloud_encryption = { workspace = true }
collections = { path = "../collections" }
concurrency_manager = { path = "../concurrency_manager", default-features = false }
crc32c = { workspace = true }
crossbeam = "0.8"
dashmap = "4.0"
derivative = "2"
dyn-clone = "1.0"
encryption = { path = "../encryption", default-features = false }
engine_rocks = { path = "../engine_rocks", default-features = false }
engine_traits = { path = "../engine_traits", default-features = false }
error_code = { path = "../error_code", default-features = false }
fail = "0.5"
fs2 = "0.4"
futures = "0.3"
futures-util = { version = "0.3.1", default-features = false, features = ["io"] }
grpcio = { version = "0.10", default-features = false, features = ["openssl-vendored", "protobuf-codec"] }
keys = { path = "../keys" }
kvengine = { path = "../kvengine" }
kvenginepb = { path = "../kvenginepb", default-features = false }
kvproto = { workspace = true }
lazy_static = "1.3"
log_wrappers = { path = "../log_wrappers" }
online_config = { path = "../online_config" }
papaya = "0.2"
pd_client = { path = "../pd_client", default-features = false }
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
protobuf = "2.8"
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raft-proto = { version = "0.7.0", default-features = false }
raftstore = { path = "../raftstore" }
rand = "0.8"
rfengine = { path = "../rfengine" }
schema = { workspace = true }
serde = "1.0"
serde_derive = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog-term = "2.4"
sst_importer = { path = "../sst_importer" }
thiserror = "1.0"
tidb_query_datatype = { path = "../tidb_query_datatype", default-features = false }
tikv_alloc = { path = "../tikv_alloc" }
tikv_util = { path = "../tikv_util", default-features = false }
time = "0.1"
tokio = { version = "1.5", features = ["full"] }
txn_types = { path = "../txn_types", default-features = false }
uuid = { version = "0.8.1", features = ["serde", "v4"] }
yatp = { git = "https://github.com/tikv/yatp.git", branch = "master" }
