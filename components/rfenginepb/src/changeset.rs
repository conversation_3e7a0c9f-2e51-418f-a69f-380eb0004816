// This file is generated by rust-protobuf 2.8.0. Do not edit
// @generated

// https://github.com/Manishearth/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![cfg_attr(rustfmt, rustfmt_skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unsafe_code)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `changeset.proto`

use protobuf::Message as Message_imported_for_functions;
use protobuf::ProtobufEnum as ProtobufEnum_imported_for_functions;

/// Generated files are compatible only with the same version
/// of protobuf runtime.
const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_8_0;

#[derive(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,Default)]
pub struct ChangeSet {
    // message fields
    pub epoch_id: u32,
    pub peers: ::protobuf::RepeatedField<PeerMeta>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ChangeSet {
    fn default() -> &'a ChangeSet {
        <ChangeSet as ::protobuf::Message>::default_instance()
    }
}

impl ChangeSet {
    pub fn new() -> ChangeSet {
        ::std::default::Default::default()
    }

    // uint32 epoch_id = 1;


    pub fn get_epoch_id(&self) -> u32 {
        self.epoch_id
    }
    pub fn clear_epoch_id(&mut self) {
        self.epoch_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_epoch_id(&mut self, v: u32) {
        self.epoch_id = v;
    }

    // repeated .rfpb.PeerMeta peers = 2;


    pub fn get_peers(&self) -> &[PeerMeta] {
        &self.peers
    }
    pub fn clear_peers(&mut self) {
        self.peers.clear();
    }

    // Param is passed by value, moved
    pub fn set_peers(&mut self, v: ::protobuf::RepeatedField<PeerMeta>) {
        self.peers = v;
    }

    // Mutable pointer to the field.
    pub fn mut_peers(&mut self) -> &mut ::protobuf::RepeatedField<PeerMeta> {
        &mut self.peers
    }

    // Take field
    pub fn take_peers(&mut self) -> ::protobuf::RepeatedField<PeerMeta> {
        ::std::mem::replace(&mut self.peers, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for ChangeSet {
    fn is_initialized(&self) -> bool {
        for v in &self.peers {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.epoch_id = tmp;
                },
                2 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.peers)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.epoch_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.epoch_id, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.peers {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.epoch_id != 0 {
            os.write_uint32(1, self.epoch_id)?;
        }
        for v in &self.peers {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ChangeSet {
        ChangeSet::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "epoch_id",
                    |m: &ChangeSet| { &m.epoch_id },
                    |m: &mut ChangeSet| { &mut m.epoch_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<PeerMeta>>(
                    "peers",
                    |m: &ChangeSet| { &m.peers },
                    |m: &mut ChangeSet| { &mut m.peers },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<ChangeSet>(
                    "ChangeSet",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static ChangeSet {
        static mut instance: ::protobuf::lazy::Lazy<ChangeSet> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ChangeSet,
        };
        unsafe {
            instance.get(ChangeSet::new)
        }
    }
}

impl ::protobuf::Clear for ChangeSet {
    fn clear(&mut self) {
        self.epoch_id = 0;
        self.peers.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for ChangeSet {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.epoch_id, "epoch_id", buf);
        ::protobuf::PbPrint::fmt(&self.peers, "peers", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for ChangeSet {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.epoch_id, "epoch_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.peers, "peers", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for ChangeSet {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct PeerMeta {
    // message fields
    pub peer_id: u64,
    pub region_id: u64,
    pub truncated_index: u64,
    pub states: ::protobuf::RepeatedField<PeerState>,
    pub files: ::protobuf::RepeatedField<RaftLogFile>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a PeerMeta {
    fn default() -> &'a PeerMeta {
        <PeerMeta as ::protobuf::Message>::default_instance()
    }
}

impl PeerMeta {
    pub fn new() -> PeerMeta {
        ::std::default::Default::default()
    }

    // uint64 peer_id = 1;


    pub fn get_peer_id(&self) -> u64 {
        self.peer_id
    }
    pub fn clear_peer_id(&mut self) {
        self.peer_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_peer_id(&mut self, v: u64) {
        self.peer_id = v;
    }

    // uint64 region_id = 2;


    pub fn get_region_id(&self) -> u64 {
        self.region_id
    }
    pub fn clear_region_id(&mut self) {
        self.region_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_region_id(&mut self, v: u64) {
        self.region_id = v;
    }

    // uint64 truncated_index = 3;


    pub fn get_truncated_index(&self) -> u64 {
        self.truncated_index
    }
    pub fn clear_truncated_index(&mut self) {
        self.truncated_index = 0;
    }

    // Param is passed by value, moved
    pub fn set_truncated_index(&mut self, v: u64) {
        self.truncated_index = v;
    }

    // repeated .rfpb.PeerState states = 4;


    pub fn get_states(&self) -> &[PeerState] {
        &self.states
    }
    pub fn clear_states(&mut self) {
        self.states.clear();
    }

    // Param is passed by value, moved
    pub fn set_states(&mut self, v: ::protobuf::RepeatedField<PeerState>) {
        self.states = v;
    }

    // Mutable pointer to the field.
    pub fn mut_states(&mut self) -> &mut ::protobuf::RepeatedField<PeerState> {
        &mut self.states
    }

    // Take field
    pub fn take_states(&mut self) -> ::protobuf::RepeatedField<PeerState> {
        ::std::mem::replace(&mut self.states, ::protobuf::RepeatedField::new())
    }

    // repeated .rfpb.RaftLogFile files = 5;


    pub fn get_files(&self) -> &[RaftLogFile] {
        &self.files
    }
    pub fn clear_files(&mut self) {
        self.files.clear();
    }

    // Param is passed by value, moved
    pub fn set_files(&mut self, v: ::protobuf::RepeatedField<RaftLogFile>) {
        self.files = v;
    }

    // Mutable pointer to the field.
    pub fn mut_files(&mut self) -> &mut ::protobuf::RepeatedField<RaftLogFile> {
        &mut self.files
    }

    // Take field
    pub fn take_files(&mut self) -> ::protobuf::RepeatedField<RaftLogFile> {
        ::std::mem::replace(&mut self.files, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for PeerMeta {
    fn is_initialized(&self) -> bool {
        for v in &self.states {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.files {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.peer_id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.region_id = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.truncated_index = tmp;
                },
                4 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.states)?;
                },
                5 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.files)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.peer_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.peer_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.region_id != 0 {
            my_size += ::protobuf::rt::value_size(2, self.region_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.truncated_index != 0 {
            my_size += ::protobuf::rt::value_size(3, self.truncated_index, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.states {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.files {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.peer_id != 0 {
            os.write_uint64(1, self.peer_id)?;
        }
        if self.region_id != 0 {
            os.write_uint64(2, self.region_id)?;
        }
        if self.truncated_index != 0 {
            os.write_uint64(3, self.truncated_index)?;
        }
        for v in &self.states {
            os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.files {
            os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> PeerMeta {
        PeerMeta::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "peer_id",
                    |m: &PeerMeta| { &m.peer_id },
                    |m: &mut PeerMeta| { &mut m.peer_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "region_id",
                    |m: &PeerMeta| { &m.region_id },
                    |m: &mut PeerMeta| { &mut m.region_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "truncated_index",
                    |m: &PeerMeta| { &m.truncated_index },
                    |m: &mut PeerMeta| { &mut m.truncated_index },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<PeerState>>(
                    "states",
                    |m: &PeerMeta| { &m.states },
                    |m: &mut PeerMeta| { &mut m.states },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RaftLogFile>>(
                    "files",
                    |m: &PeerMeta| { &m.files },
                    |m: &mut PeerMeta| { &mut m.files },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<PeerMeta>(
                    "PeerMeta",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static PeerMeta {
        static mut instance: ::protobuf::lazy::Lazy<PeerMeta> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const PeerMeta,
        };
        unsafe {
            instance.get(PeerMeta::new)
        }
    }
}

impl ::protobuf::Clear for PeerMeta {
    fn clear(&mut self) {
        self.peer_id = 0;
        self.region_id = 0;
        self.truncated_index = 0;
        self.states.clear();
        self.files.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for PeerMeta {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.peer_id, "peer_id", buf);
        ::protobuf::PbPrint::fmt(&self.region_id, "region_id", buf);
        ::protobuf::PbPrint::fmt(&self.truncated_index, "truncated_index", buf);
        ::protobuf::PbPrint::fmt(&self.states, "states", buf);
        ::protobuf::PbPrint::fmt(&self.files, "files", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for PeerMeta {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.peer_id, "peer_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.region_id, "region_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.truncated_index, "truncated_index", &mut s);
        ::protobuf::PbPrint::fmt(&self.states, "states", &mut s);
        ::protobuf::PbPrint::fmt(&self.files, "files", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for PeerMeta {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct PeerState {
    // message fields
    pub key: ::std::vec::Vec<u8>,
    pub value: ::std::vec::Vec<u8>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a PeerState {
    fn default() -> &'a PeerState {
        <PeerState as ::protobuf::Message>::default_instance()
    }
}

impl PeerState {
    pub fn new() -> PeerState {
        ::std::default::Default::default()
    }

    // bytes key = 1;


    pub fn get_key(&self) -> &[u8] {
        &self.key
    }
    pub fn clear_key(&mut self) {
        self.key.clear();
    }

    // Param is passed by value, moved
    pub fn set_key(&mut self, v: ::std::vec::Vec<u8>) {
        self.key = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_key(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.key
    }

    // Take field
    pub fn take_key(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.key, ::std::vec::Vec::new())
    }

    // bytes value = 2;


    pub fn get_value(&self) -> &[u8] {
        &self.value
    }
    pub fn clear_value(&mut self) {
        self.value.clear();
    }

    // Param is passed by value, moved
    pub fn set_value(&mut self, v: ::std::vec::Vec<u8>) {
        self.value = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_value(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.value
    }

    // Take field
    pub fn take_value(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.value, ::std::vec::Vec::new())
    }
}

impl ::protobuf::Message for PeerState {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.key)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.value)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.key.is_empty() {
            my_size += ::protobuf::rt::bytes_size(1, &self.key);
        }
        if !self.value.is_empty() {
            my_size += ::protobuf::rt::bytes_size(2, &self.value);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if !self.key.is_empty() {
            os.write_bytes(1, &self.key)?;
        }
        if !self.value.is_empty() {
            os.write_bytes(2, &self.value)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> PeerState {
        PeerState::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "key",
                    |m: &PeerState| { &m.key },
                    |m: &mut PeerState| { &mut m.key },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "value",
                    |m: &PeerState| { &m.value },
                    |m: &mut PeerState| { &mut m.value },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<PeerState>(
                    "PeerState",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static PeerState {
        static mut instance: ::protobuf::lazy::Lazy<PeerState> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const PeerState,
        };
        unsafe {
            instance.get(PeerState::new)
        }
    }
}

impl ::protobuf::Clear for PeerState {
    fn clear(&mut self) {
        self.key.clear();
        self.value.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for PeerState {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.key, "key", buf);
        ::protobuf::PbPrint::fmt(&self.value, "value", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for PeerState {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.key, "key", &mut s);
        ::protobuf::PbPrint::fmt(&self.value, "value", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for PeerState {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RaftLogFile {
    // message fields
    pub first_index: u64,
    pub last_index: u64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RaftLogFile {
    fn default() -> &'a RaftLogFile {
        <RaftLogFile as ::protobuf::Message>::default_instance()
    }
}

impl RaftLogFile {
    pub fn new() -> RaftLogFile {
        ::std::default::Default::default()
    }

    // uint64 first_index = 1;


    pub fn get_first_index(&self) -> u64 {
        self.first_index
    }
    pub fn clear_first_index(&mut self) {
        self.first_index = 0;
    }

    // Param is passed by value, moved
    pub fn set_first_index(&mut self, v: u64) {
        self.first_index = v;
    }

    // uint64 last_index = 2;


    pub fn get_last_index(&self) -> u64 {
        self.last_index
    }
    pub fn clear_last_index(&mut self) {
        self.last_index = 0;
    }

    // Param is passed by value, moved
    pub fn set_last_index(&mut self, v: u64) {
        self.last_index = v;
    }
}

impl ::protobuf::Message for RaftLogFile {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.first_index = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.last_index = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.first_index != 0 {
            my_size += ::protobuf::rt::value_size(1, self.first_index, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.last_index != 0 {
            my_size += ::protobuf::rt::value_size(2, self.last_index, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.first_index != 0 {
            os.write_uint64(1, self.first_index)?;
        }
        if self.last_index != 0 {
            os.write_uint64(2, self.last_index)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RaftLogFile {
        RaftLogFile::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "first_index",
                    |m: &RaftLogFile| { &m.first_index },
                    |m: &mut RaftLogFile| { &mut m.first_index },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "last_index",
                    |m: &RaftLogFile| { &m.last_index },
                    |m: &mut RaftLogFile| { &mut m.last_index },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<RaftLogFile>(
                    "RaftLogFile",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static RaftLogFile {
        static mut instance: ::protobuf::lazy::Lazy<RaftLogFile> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const RaftLogFile,
        };
        unsafe {
            instance.get(RaftLogFile::new)
        }
    }
}

impl ::protobuf::Clear for RaftLogFile {
    fn clear(&mut self) {
        self.first_index = 0;
        self.last_index = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for RaftLogFile {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.first_index, "first_index", buf);
        ::protobuf::PbPrint::fmt(&self.last_index, "last_index", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for RaftLogFile {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.first_index, "first_index", &mut s);
        ::protobuf::PbPrint::fmt(&self.last_index, "last_index", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for RaftLogFile {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct StoreBackupMeta {
    // message fields
    pub store_id: u64,
    pub manifest: ::protobuf::SingularPtrField<ChangeSet>,
    pub wal_chunks: ::protobuf::RepeatedField<WalChunk>,
    pub raft_meta_start_off: u64,
    pub epoch: u32,
    pub offset: u64,
    pub keyspace_size: ::std::collections::HashMap<u32, BackupSize>,
    pub has_missing_commit_record: bool,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a StoreBackupMeta {
    fn default() -> &'a StoreBackupMeta {
        <StoreBackupMeta as ::protobuf::Message>::default_instance()
    }
}

impl StoreBackupMeta {
    pub fn new() -> StoreBackupMeta {
        ::std::default::Default::default()
    }

    // uint64 store_id = 1;


    pub fn get_store_id(&self) -> u64 {
        self.store_id
    }
    pub fn clear_store_id(&mut self) {
        self.store_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_store_id(&mut self, v: u64) {
        self.store_id = v;
    }

    // .rfpb.ChangeSet manifest = 2;


    pub fn get_manifest(&self) -> &ChangeSet {
        self.manifest.as_ref().unwrap_or_else(|| ChangeSet::default_instance())
    }
    pub fn clear_manifest(&mut self) {
        self.manifest.clear();
    }

    pub fn has_manifest(&self) -> bool {
        self.manifest.is_some()
    }

    // Param is passed by value, moved
    pub fn set_manifest(&mut self, v: ChangeSet) {
        self.manifest = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_manifest(&mut self) -> &mut ChangeSet {
        if self.manifest.is_none() {
            self.manifest.set_default();
        }
        self.manifest.as_mut().unwrap()
    }

    // Take field
    pub fn take_manifest(&mut self) -> ChangeSet {
        self.manifest.take().unwrap_or_else(|| ChangeSet::new())
    }

    // repeated .rfpb.WalChunk wal_chunks = 3;


    pub fn get_wal_chunks(&self) -> &[WalChunk] {
        &self.wal_chunks
    }
    pub fn clear_wal_chunks(&mut self) {
        self.wal_chunks.clear();
    }

    // Param is passed by value, moved
    pub fn set_wal_chunks(&mut self, v: ::protobuf::RepeatedField<WalChunk>) {
        self.wal_chunks = v;
    }

    // Mutable pointer to the field.
    pub fn mut_wal_chunks(&mut self) -> &mut ::protobuf::RepeatedField<WalChunk> {
        &mut self.wal_chunks
    }

    // Take field
    pub fn take_wal_chunks(&mut self) -> ::protobuf::RepeatedField<WalChunk> {
        ::std::mem::replace(&mut self.wal_chunks, ::protobuf::RepeatedField::new())
    }

    // uint64 raft_meta_start_off = 4;


    pub fn get_raft_meta_start_off(&self) -> u64 {
        self.raft_meta_start_off
    }
    pub fn clear_raft_meta_start_off(&mut self) {
        self.raft_meta_start_off = 0;
    }

    // Param is passed by value, moved
    pub fn set_raft_meta_start_off(&mut self, v: u64) {
        self.raft_meta_start_off = v;
    }

    // uint32 epoch = 5;


    pub fn get_epoch(&self) -> u32 {
        self.epoch
    }
    pub fn clear_epoch(&mut self) {
        self.epoch = 0;
    }

    // Param is passed by value, moved
    pub fn set_epoch(&mut self, v: u32) {
        self.epoch = v;
    }

    // uint64 offset = 6;


    pub fn get_offset(&self) -> u64 {
        self.offset
    }
    pub fn clear_offset(&mut self) {
        self.offset = 0;
    }

    // Param is passed by value, moved
    pub fn set_offset(&mut self, v: u64) {
        self.offset = v;
    }

    // repeated .rfpb.StoreBackupMeta.keyspace_size_MapEntry keyspace_size = 7;


    pub fn get_keyspace_size(&self) -> &::std::collections::HashMap<u32, BackupSize> {
        &self.keyspace_size
    }
    pub fn clear_keyspace_size(&mut self) {
        self.keyspace_size.clear();
    }

    // Param is passed by value, moved
    pub fn set_keyspace_size(&mut self, v: ::std::collections::HashMap<u32, BackupSize>) {
        self.keyspace_size = v;
    }

    // Mutable pointer to the field.
    pub fn mut_keyspace_size(&mut self) -> &mut ::std::collections::HashMap<u32, BackupSize> {
        &mut self.keyspace_size
    }

    // Take field
    pub fn take_keyspace_size(&mut self) -> ::std::collections::HashMap<u32, BackupSize> {
        ::std::mem::replace(&mut self.keyspace_size, ::std::collections::HashMap::new())
    }

    // bool has_missing_commit_record = 8;


    pub fn get_has_missing_commit_record(&self) -> bool {
        self.has_missing_commit_record
    }
    pub fn clear_has_missing_commit_record(&mut self) {
        self.has_missing_commit_record = false;
    }

    // Param is passed by value, moved
    pub fn set_has_missing_commit_record(&mut self, v: bool) {
        self.has_missing_commit_record = v;
    }
}

impl ::protobuf::Message for StoreBackupMeta {
    fn is_initialized(&self) -> bool {
        for v in &self.manifest {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.wal_chunks {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.store_id = tmp;
                },
                2 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.manifest)?;
                },
                3 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.wal_chunks)?;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.raft_meta_start_off = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.epoch = tmp;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.offset = tmp;
                },
                7 => {
                    ::protobuf::rt::read_map_into::<::protobuf::types::ProtobufTypeUint32, ::protobuf::types::ProtobufTypeMessage<BackupSize>>(wire_type, is, &mut self.keyspace_size)?;
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.has_missing_commit_record = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.store_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.store_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if let Some(ref v) = self.manifest.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        for value in &self.wal_chunks {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if self.raft_meta_start_off != 0 {
            my_size += ::protobuf::rt::value_size(4, self.raft_meta_start_off, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.epoch != 0 {
            my_size += ::protobuf::rt::value_size(5, self.epoch, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.offset != 0 {
            my_size += ::protobuf::rt::value_size(6, self.offset, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::compute_map_size::<::protobuf::types::ProtobufTypeUint32, ::protobuf::types::ProtobufTypeMessage<BackupSize>>(7, &self.keyspace_size);
        if self.has_missing_commit_record != false {
            my_size += 2;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.store_id != 0 {
            os.write_uint64(1, self.store_id)?;
        }
        if let Some(ref v) = self.manifest.as_ref() {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        for v in &self.wal_chunks {
            os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if self.raft_meta_start_off != 0 {
            os.write_uint64(4, self.raft_meta_start_off)?;
        }
        if self.epoch != 0 {
            os.write_uint32(5, self.epoch)?;
        }
        if self.offset != 0 {
            os.write_uint64(6, self.offset)?;
        }
        ::protobuf::rt::write_map_with_cached_sizes::<::protobuf::types::ProtobufTypeUint32, ::protobuf::types::ProtobufTypeMessage<BackupSize>>(7, &self.keyspace_size, os)?;
        if self.has_missing_commit_record != false {
            os.write_bool(8, self.has_missing_commit_record)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> StoreBackupMeta {
        StoreBackupMeta::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "store_id",
                    |m: &StoreBackupMeta| { &m.store_id },
                    |m: &mut StoreBackupMeta| { &mut m.store_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ChangeSet>>(
                    "manifest",
                    |m: &StoreBackupMeta| { &m.manifest },
                    |m: &mut StoreBackupMeta| { &mut m.manifest },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<WalChunk>>(
                    "wal_chunks",
                    |m: &StoreBackupMeta| { &m.wal_chunks },
                    |m: &mut StoreBackupMeta| { &mut m.wal_chunks },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "raft_meta_start_off",
                    |m: &StoreBackupMeta| { &m.raft_meta_start_off },
                    |m: &mut StoreBackupMeta| { &mut m.raft_meta_start_off },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "epoch",
                    |m: &StoreBackupMeta| { &m.epoch },
                    |m: &mut StoreBackupMeta| { &mut m.epoch },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "offset",
                    |m: &StoreBackupMeta| { &m.offset },
                    |m: &mut StoreBackupMeta| { &mut m.offset },
                ));
                fields.push(::protobuf::reflect::accessor::make_map_accessor::<_, ::protobuf::types::ProtobufTypeUint32, ::protobuf::types::ProtobufTypeMessage<BackupSize>>(
                    "keyspace_size",
                    |m: &StoreBackupMeta| { &m.keyspace_size },
                    |m: &mut StoreBackupMeta| { &mut m.keyspace_size },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                    "has_missing_commit_record",
                    |m: &StoreBackupMeta| { &m.has_missing_commit_record },
                    |m: &mut StoreBackupMeta| { &mut m.has_missing_commit_record },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<StoreBackupMeta>(
                    "StoreBackupMeta",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static StoreBackupMeta {
        static mut instance: ::protobuf::lazy::Lazy<StoreBackupMeta> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const StoreBackupMeta,
        };
        unsafe {
            instance.get(StoreBackupMeta::new)
        }
    }
}

impl ::protobuf::Clear for StoreBackupMeta {
    fn clear(&mut self) {
        self.store_id = 0;
        self.manifest.clear();
        self.wal_chunks.clear();
        self.raft_meta_start_off = 0;
        self.epoch = 0;
        self.offset = 0;
        self.keyspace_size.clear();
        self.has_missing_commit_record = false;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for StoreBackupMeta {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.store_id, "store_id", buf);
        ::protobuf::PbPrint::fmt(&self.manifest, "manifest", buf);
        ::protobuf::PbPrint::fmt(&self.wal_chunks, "wal_chunks", buf);
        ::protobuf::PbPrint::fmt(&self.raft_meta_start_off, "raft_meta_start_off", buf);
        ::protobuf::PbPrint::fmt(&self.epoch, "epoch", buf);
        ::protobuf::PbPrint::fmt(&self.offset, "offset", buf);
        ::protobuf::PbPrint::fmt(&self.keyspace_size, "keyspace_size", buf);
        ::protobuf::PbPrint::fmt(&self.has_missing_commit_record, "has_missing_commit_record", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for StoreBackupMeta {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.store_id, "store_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.manifest, "manifest", &mut s);
        ::protobuf::PbPrint::fmt(&self.wal_chunks, "wal_chunks", &mut s);
        ::protobuf::PbPrint::fmt(&self.raft_meta_start_off, "raft_meta_start_off", &mut s);
        ::protobuf::PbPrint::fmt(&self.epoch, "epoch", &mut s);
        ::protobuf::PbPrint::fmt(&self.offset, "offset", &mut s);
        ::protobuf::PbPrint::fmt(&self.keyspace_size, "keyspace_size", &mut s);
        ::protobuf::PbPrint::fmt(&self.has_missing_commit_record, "has_missing_commit_record", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for StoreBackupMeta {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct BackupSize {
    // message fields
    pub size: u64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a BackupSize {
    fn default() -> &'a BackupSize {
        <BackupSize as ::protobuf::Message>::default_instance()
    }
}

impl BackupSize {
    pub fn new() -> BackupSize {
        ::std::default::Default::default()
    }

    // uint64 size = 1;


    pub fn get_size(&self) -> u64 {
        self.size
    }
    pub fn clear_size(&mut self) {
        self.size = 0;
    }

    // Param is passed by value, moved
    pub fn set_size(&mut self, v: u64) {
        self.size = v;
    }
}

impl ::protobuf::Message for BackupSize {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.size = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.size != 0 {
            my_size += ::protobuf::rt::value_size(1, self.size, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.size != 0 {
            os.write_uint64(1, self.size)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> BackupSize {
        BackupSize::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "size",
                    |m: &BackupSize| { &m.size },
                    |m: &mut BackupSize| { &mut m.size },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<BackupSize>(
                    "BackupSize",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static BackupSize {
        static mut instance: ::protobuf::lazy::Lazy<BackupSize> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const BackupSize,
        };
        unsafe {
            instance.get(BackupSize::new)
        }
    }
}

impl ::protobuf::Clear for BackupSize {
    fn clear(&mut self) {
        self.size = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for BackupSize {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.size, "size", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for BackupSize {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.size, "size", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for BackupSize {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct WalChunk {
    // message fields
    pub epoch: u32,
    pub start_off: u64,
    pub end_off: u64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a WalChunk {
    fn default() -> &'a WalChunk {
        <WalChunk as ::protobuf::Message>::default_instance()
    }
}

impl WalChunk {
    pub fn new() -> WalChunk {
        ::std::default::Default::default()
    }

    // uint32 epoch = 1;


    pub fn get_epoch(&self) -> u32 {
        self.epoch
    }
    pub fn clear_epoch(&mut self) {
        self.epoch = 0;
    }

    // Param is passed by value, moved
    pub fn set_epoch(&mut self, v: u32) {
        self.epoch = v;
    }

    // uint64 start_off = 2;


    pub fn get_start_off(&self) -> u64 {
        self.start_off
    }
    pub fn clear_start_off(&mut self) {
        self.start_off = 0;
    }

    // Param is passed by value, moved
    pub fn set_start_off(&mut self, v: u64) {
        self.start_off = v;
    }

    // uint64 end_off = 3;


    pub fn get_end_off(&self) -> u64 {
        self.end_off
    }
    pub fn clear_end_off(&mut self) {
        self.end_off = 0;
    }

    // Param is passed by value, moved
    pub fn set_end_off(&mut self, v: u64) {
        self.end_off = v;
    }
}

impl ::protobuf::Message for WalChunk {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.epoch = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.start_off = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.end_off = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.epoch != 0 {
            my_size += ::protobuf::rt::value_size(1, self.epoch, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.start_off != 0 {
            my_size += ::protobuf::rt::value_size(2, self.start_off, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.end_off != 0 {
            my_size += ::protobuf::rt::value_size(3, self.end_off, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.epoch != 0 {
            os.write_uint32(1, self.epoch)?;
        }
        if self.start_off != 0 {
            os.write_uint64(2, self.start_off)?;
        }
        if self.end_off != 0 {
            os.write_uint64(3, self.end_off)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> WalChunk {
        WalChunk::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "epoch",
                    |m: &WalChunk| { &m.epoch },
                    |m: &mut WalChunk| { &mut m.epoch },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "start_off",
                    |m: &WalChunk| { &m.start_off },
                    |m: &mut WalChunk| { &mut m.start_off },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "end_off",
                    |m: &WalChunk| { &m.end_off },
                    |m: &mut WalChunk| { &mut m.end_off },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<WalChunk>(
                    "WalChunk",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static WalChunk {
        static mut instance: ::protobuf::lazy::Lazy<WalChunk> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const WalChunk,
        };
        unsafe {
            instance.get(WalChunk::new)
        }
    }
}

impl ::protobuf::Clear for WalChunk {
    fn clear(&mut self) {
        self.epoch = 0;
        self.start_off = 0;
        self.end_off = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for WalChunk {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.epoch, "epoch", buf);
        ::protobuf::PbPrint::fmt(&self.start_off, "start_off", buf);
        ::protobuf::PbPrint::fmt(&self.end_off, "end_off", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for WalChunk {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.epoch, "epoch", &mut s);
        ::protobuf::PbPrint::fmt(&self.start_off, "start_off", &mut s);
        ::protobuf::PbPrint::fmt(&self.end_off, "end_off", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for WalChunk {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ClusterBackupMeta {
    // message fields
    pub stores: ::protobuf::RepeatedField<StoreBackupMeta>,
    pub cluster_id: u64,
    pub backup_ts: u64,
    pub alloc_id: u64,
    pub safe_ts: u64,
    pub keyspace_meta: ::std::collections::HashMap<::std::vec::Vec<u8>, ::std::vec::Vec<u8>>,
    pub meta_revision: i64,
    pub is_lightweight: bool,
    pub tolerated_err: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ClusterBackupMeta {
    fn default() -> &'a ClusterBackupMeta {
        <ClusterBackupMeta as ::protobuf::Message>::default_instance()
    }
}

impl ClusterBackupMeta {
    pub fn new() -> ClusterBackupMeta {
        ::std::default::Default::default()
    }

    // repeated .rfpb.StoreBackupMeta stores = 1;


    pub fn get_stores(&self) -> &[StoreBackupMeta] {
        &self.stores
    }
    pub fn clear_stores(&mut self) {
        self.stores.clear();
    }

    // Param is passed by value, moved
    pub fn set_stores(&mut self, v: ::protobuf::RepeatedField<StoreBackupMeta>) {
        self.stores = v;
    }

    // Mutable pointer to the field.
    pub fn mut_stores(&mut self) -> &mut ::protobuf::RepeatedField<StoreBackupMeta> {
        &mut self.stores
    }

    // Take field
    pub fn take_stores(&mut self) -> ::protobuf::RepeatedField<StoreBackupMeta> {
        ::std::mem::replace(&mut self.stores, ::protobuf::RepeatedField::new())
    }

    // uint64 cluster_id = 2;


    pub fn get_cluster_id(&self) -> u64 {
        self.cluster_id
    }
    pub fn clear_cluster_id(&mut self) {
        self.cluster_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_cluster_id(&mut self, v: u64) {
        self.cluster_id = v;
    }

    // uint64 backup_ts = 3;


    pub fn get_backup_ts(&self) -> u64 {
        self.backup_ts
    }
    pub fn clear_backup_ts(&mut self) {
        self.backup_ts = 0;
    }

    // Param is passed by value, moved
    pub fn set_backup_ts(&mut self, v: u64) {
        self.backup_ts = v;
    }

    // uint64 alloc_id = 4;


    pub fn get_alloc_id(&self) -> u64 {
        self.alloc_id
    }
    pub fn clear_alloc_id(&mut self) {
        self.alloc_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_alloc_id(&mut self, v: u64) {
        self.alloc_id = v;
    }

    // uint64 safe_ts = 5;


    pub fn get_safe_ts(&self) -> u64 {
        self.safe_ts
    }
    pub fn clear_safe_ts(&mut self) {
        self.safe_ts = 0;
    }

    // Param is passed by value, moved
    pub fn set_safe_ts(&mut self, v: u64) {
        self.safe_ts = v;
    }

    // repeated .rfpb.ClusterBackupMeta.keyspace_meta_MapEntry keyspace_meta = 6;


    pub fn get_keyspace_meta(&self) -> &::std::collections::HashMap<::std::vec::Vec<u8>, ::std::vec::Vec<u8>> {
        &self.keyspace_meta
    }
    pub fn clear_keyspace_meta(&mut self) {
        self.keyspace_meta.clear();
    }

    // Param is passed by value, moved
    pub fn set_keyspace_meta(&mut self, v: ::std::collections::HashMap<::std::vec::Vec<u8>, ::std::vec::Vec<u8>>) {
        self.keyspace_meta = v;
    }

    // Mutable pointer to the field.
    pub fn mut_keyspace_meta(&mut self) -> &mut ::std::collections::HashMap<::std::vec::Vec<u8>, ::std::vec::Vec<u8>> {
        &mut self.keyspace_meta
    }

    // Take field
    pub fn take_keyspace_meta(&mut self) -> ::std::collections::HashMap<::std::vec::Vec<u8>, ::std::vec::Vec<u8>> {
        ::std::mem::replace(&mut self.keyspace_meta, ::std::collections::HashMap::new())
    }

    // int64 meta_revision = 7;


    pub fn get_meta_revision(&self) -> i64 {
        self.meta_revision
    }
    pub fn clear_meta_revision(&mut self) {
        self.meta_revision = 0;
    }

    // Param is passed by value, moved
    pub fn set_meta_revision(&mut self, v: i64) {
        self.meta_revision = v;
    }

    // bool is_lightweight = 8;


    pub fn get_is_lightweight(&self) -> bool {
        self.is_lightweight
    }
    pub fn clear_is_lightweight(&mut self) {
        self.is_lightweight = false;
    }

    // Param is passed by value, moved
    pub fn set_is_lightweight(&mut self, v: bool) {
        self.is_lightweight = v;
    }

    // uint32 tolerated_err = 9;


    pub fn get_tolerated_err(&self) -> u32 {
        self.tolerated_err
    }
    pub fn clear_tolerated_err(&mut self) {
        self.tolerated_err = 0;
    }

    // Param is passed by value, moved
    pub fn set_tolerated_err(&mut self, v: u32) {
        self.tolerated_err = v;
    }
}

impl ::protobuf::Message for ClusterBackupMeta {
    fn is_initialized(&self) -> bool {
        for v in &self.stores {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.stores)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.cluster_id = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.backup_ts = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.alloc_id = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.safe_ts = tmp;
                },
                6 => {
                    ::protobuf::rt::read_map_into::<::protobuf::types::ProtobufTypeBytes, ::protobuf::types::ProtobufTypeBytes>(wire_type, is, &mut self.keyspace_meta)?;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.meta_revision = tmp;
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.is_lightweight = tmp;
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.tolerated_err = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.stores {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if self.cluster_id != 0 {
            my_size += ::protobuf::rt::value_size(2, self.cluster_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.backup_ts != 0 {
            my_size += ::protobuf::rt::value_size(3, self.backup_ts, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.alloc_id != 0 {
            my_size += ::protobuf::rt::value_size(4, self.alloc_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.safe_ts != 0 {
            my_size += ::protobuf::rt::value_size(5, self.safe_ts, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::compute_map_size::<::protobuf::types::ProtobufTypeBytes, ::protobuf::types::ProtobufTypeBytes>(6, &self.keyspace_meta);
        if self.meta_revision != 0 {
            my_size += ::protobuf::rt::value_size(7, self.meta_revision, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.is_lightweight != false {
            my_size += 2;
        }
        if self.tolerated_err != 0 {
            my_size += ::protobuf::rt::value_size(9, self.tolerated_err, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        for v in &self.stores {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if self.cluster_id != 0 {
            os.write_uint64(2, self.cluster_id)?;
        }
        if self.backup_ts != 0 {
            os.write_uint64(3, self.backup_ts)?;
        }
        if self.alloc_id != 0 {
            os.write_uint64(4, self.alloc_id)?;
        }
        if self.safe_ts != 0 {
            os.write_uint64(5, self.safe_ts)?;
        }
        ::protobuf::rt::write_map_with_cached_sizes::<::protobuf::types::ProtobufTypeBytes, ::protobuf::types::ProtobufTypeBytes>(6, &self.keyspace_meta, os)?;
        if self.meta_revision != 0 {
            os.write_int64(7, self.meta_revision)?;
        }
        if self.is_lightweight != false {
            os.write_bool(8, self.is_lightweight)?;
        }
        if self.tolerated_err != 0 {
            os.write_uint32(9, self.tolerated_err)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ClusterBackupMeta {
        ClusterBackupMeta::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<StoreBackupMeta>>(
                    "stores",
                    |m: &ClusterBackupMeta| { &m.stores },
                    |m: &mut ClusterBackupMeta| { &mut m.stores },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "cluster_id",
                    |m: &ClusterBackupMeta| { &m.cluster_id },
                    |m: &mut ClusterBackupMeta| { &mut m.cluster_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "backup_ts",
                    |m: &ClusterBackupMeta| { &m.backup_ts },
                    |m: &mut ClusterBackupMeta| { &mut m.backup_ts },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "alloc_id",
                    |m: &ClusterBackupMeta| { &m.alloc_id },
                    |m: &mut ClusterBackupMeta| { &mut m.alloc_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "safe_ts",
                    |m: &ClusterBackupMeta| { &m.safe_ts },
                    |m: &mut ClusterBackupMeta| { &mut m.safe_ts },
                ));
                fields.push(::protobuf::reflect::accessor::make_map_accessor::<_, ::protobuf::types::ProtobufTypeBytes, ::protobuf::types::ProtobufTypeBytes>(
                    "keyspace_meta",
                    |m: &ClusterBackupMeta| { &m.keyspace_meta },
                    |m: &mut ClusterBackupMeta| { &mut m.keyspace_meta },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "meta_revision",
                    |m: &ClusterBackupMeta| { &m.meta_revision },
                    |m: &mut ClusterBackupMeta| { &mut m.meta_revision },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                    "is_lightweight",
                    |m: &ClusterBackupMeta| { &m.is_lightweight },
                    |m: &mut ClusterBackupMeta| { &mut m.is_lightweight },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "tolerated_err",
                    |m: &ClusterBackupMeta| { &m.tolerated_err },
                    |m: &mut ClusterBackupMeta| { &mut m.tolerated_err },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<ClusterBackupMeta>(
                    "ClusterBackupMeta",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static ClusterBackupMeta {
        static mut instance: ::protobuf::lazy::Lazy<ClusterBackupMeta> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ClusterBackupMeta,
        };
        unsafe {
            instance.get(ClusterBackupMeta::new)
        }
    }
}

impl ::protobuf::Clear for ClusterBackupMeta {
    fn clear(&mut self) {
        self.stores.clear();
        self.cluster_id = 0;
        self.backup_ts = 0;
        self.alloc_id = 0;
        self.safe_ts = 0;
        self.keyspace_meta.clear();
        self.meta_revision = 0;
        self.is_lightweight = false;
        self.tolerated_err = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for ClusterBackupMeta {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.stores, "stores", buf);
        ::protobuf::PbPrint::fmt(&self.cluster_id, "cluster_id", buf);
        ::protobuf::PbPrint::fmt(&self.backup_ts, "backup_ts", buf);
        ::protobuf::PbPrint::fmt(&self.alloc_id, "alloc_id", buf);
        ::protobuf::PbPrint::fmt(&self.safe_ts, "safe_ts", buf);
        ::protobuf::PbPrint::fmt(&self.keyspace_meta, "keyspace_meta", buf);
        ::protobuf::PbPrint::fmt(&self.meta_revision, "meta_revision", buf);
        ::protobuf::PbPrint::fmt(&self.is_lightweight, "is_lightweight", buf);
        ::protobuf::PbPrint::fmt(&self.tolerated_err, "tolerated_err", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for ClusterBackupMeta {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.stores, "stores", &mut s);
        ::protobuf::PbPrint::fmt(&self.cluster_id, "cluster_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.backup_ts, "backup_ts", &mut s);
        ::protobuf::PbPrint::fmt(&self.alloc_id, "alloc_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.safe_ts, "safe_ts", &mut s);
        ::protobuf::PbPrint::fmt(&self.keyspace_meta, "keyspace_meta", &mut s);
        ::protobuf::PbPrint::fmt(&self.meta_revision, "meta_revision", &mut s);
        ::protobuf::PbPrint::fmt(&self.is_lightweight, "is_lightweight", &mut s);
        ::protobuf::PbPrint::fmt(&self.tolerated_err, "tolerated_err", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for ClusterBackupMeta {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RaftLogBackupFile {
    // message fields
    pub peer_id: u64,
    pub first_index: u64,
    pub last_index: u64,
    pub start_off: u64,
    pub end_off: u64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RaftLogBackupFile {
    fn default() -> &'a RaftLogBackupFile {
        <RaftLogBackupFile as ::protobuf::Message>::default_instance()
    }
}

impl RaftLogBackupFile {
    pub fn new() -> RaftLogBackupFile {
        ::std::default::Default::default()
    }

    // uint64 peer_id = 1;


    pub fn get_peer_id(&self) -> u64 {
        self.peer_id
    }
    pub fn clear_peer_id(&mut self) {
        self.peer_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_peer_id(&mut self, v: u64) {
        self.peer_id = v;
    }

    // uint64 first_index = 2;


    pub fn get_first_index(&self) -> u64 {
        self.first_index
    }
    pub fn clear_first_index(&mut self) {
        self.first_index = 0;
    }

    // Param is passed by value, moved
    pub fn set_first_index(&mut self, v: u64) {
        self.first_index = v;
    }

    // uint64 last_index = 3;


    pub fn get_last_index(&self) -> u64 {
        self.last_index
    }
    pub fn clear_last_index(&mut self) {
        self.last_index = 0;
    }

    // Param is passed by value, moved
    pub fn set_last_index(&mut self, v: u64) {
        self.last_index = v;
    }

    // uint64 start_off = 4;


    pub fn get_start_off(&self) -> u64 {
        self.start_off
    }
    pub fn clear_start_off(&mut self) {
        self.start_off = 0;
    }

    // Param is passed by value, moved
    pub fn set_start_off(&mut self, v: u64) {
        self.start_off = v;
    }

    // uint64 end_off = 5;


    pub fn get_end_off(&self) -> u64 {
        self.end_off
    }
    pub fn clear_end_off(&mut self) {
        self.end_off = 0;
    }

    // Param is passed by value, moved
    pub fn set_end_off(&mut self, v: u64) {
        self.end_off = v;
    }
}

impl ::protobuf::Message for RaftLogBackupFile {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.peer_id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.first_index = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.last_index = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.start_off = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.end_off = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.peer_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.peer_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.first_index != 0 {
            my_size += ::protobuf::rt::value_size(2, self.first_index, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.last_index != 0 {
            my_size += ::protobuf::rt::value_size(3, self.last_index, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.start_off != 0 {
            my_size += ::protobuf::rt::value_size(4, self.start_off, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.end_off != 0 {
            my_size += ::protobuf::rt::value_size(5, self.end_off, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.peer_id != 0 {
            os.write_uint64(1, self.peer_id)?;
        }
        if self.first_index != 0 {
            os.write_uint64(2, self.first_index)?;
        }
        if self.last_index != 0 {
            os.write_uint64(3, self.last_index)?;
        }
        if self.start_off != 0 {
            os.write_uint64(4, self.start_off)?;
        }
        if self.end_off != 0 {
            os.write_uint64(5, self.end_off)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RaftLogBackupFile {
        RaftLogBackupFile::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "peer_id",
                    |m: &RaftLogBackupFile| { &m.peer_id },
                    |m: &mut RaftLogBackupFile| { &mut m.peer_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "first_index",
                    |m: &RaftLogBackupFile| { &m.first_index },
                    |m: &mut RaftLogBackupFile| { &mut m.first_index },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "last_index",
                    |m: &RaftLogBackupFile| { &m.last_index },
                    |m: &mut RaftLogBackupFile| { &mut m.last_index },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "start_off",
                    |m: &RaftLogBackupFile| { &m.start_off },
                    |m: &mut RaftLogBackupFile| { &mut m.start_off },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "end_off",
                    |m: &RaftLogBackupFile| { &m.end_off },
                    |m: &mut RaftLogBackupFile| { &mut m.end_off },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<RaftLogBackupFile>(
                    "RaftLogBackupFile",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static RaftLogBackupFile {
        static mut instance: ::protobuf::lazy::Lazy<RaftLogBackupFile> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const RaftLogBackupFile,
        };
        unsafe {
            instance.get(RaftLogBackupFile::new)
        }
    }
}

impl ::protobuf::Clear for RaftLogBackupFile {
    fn clear(&mut self) {
        self.peer_id = 0;
        self.first_index = 0;
        self.last_index = 0;
        self.start_off = 0;
        self.end_off = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for RaftLogBackupFile {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.peer_id, "peer_id", buf);
        ::protobuf::PbPrint::fmt(&self.first_index, "first_index", buf);
        ::protobuf::PbPrint::fmt(&self.last_index, "last_index", buf);
        ::protobuf::PbPrint::fmt(&self.start_off, "start_off", buf);
        ::protobuf::PbPrint::fmt(&self.end_off, "end_off", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for RaftLogBackupFile {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.peer_id, "peer_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.first_index, "first_index", &mut s);
        ::protobuf::PbPrint::fmt(&self.last_index, "last_index", &mut s);
        ::protobuf::PbPrint::fmt(&self.start_off, "start_off", &mut s);
        ::protobuf::PbPrint::fmt(&self.end_off, "end_off", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for RaftLogBackupFile {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct KeySpaceBackupMeta {
    // message fields
    pub keyspace_id: u32,
    pub files: ::protobuf::RepeatedField<RaftLogBackupFile>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a KeySpaceBackupMeta {
    fn default() -> &'a KeySpaceBackupMeta {
        <KeySpaceBackupMeta as ::protobuf::Message>::default_instance()
    }
}

impl KeySpaceBackupMeta {
    pub fn new() -> KeySpaceBackupMeta {
        ::std::default::Default::default()
    }

    // uint32 keyspace_id = 1;


    pub fn get_keyspace_id(&self) -> u32 {
        self.keyspace_id
    }
    pub fn clear_keyspace_id(&mut self) {
        self.keyspace_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_keyspace_id(&mut self, v: u32) {
        self.keyspace_id = v;
    }

    // repeated .rfpb.RaftLogBackupFile files = 2;


    pub fn get_files(&self) -> &[RaftLogBackupFile] {
        &self.files
    }
    pub fn clear_files(&mut self) {
        self.files.clear();
    }

    // Param is passed by value, moved
    pub fn set_files(&mut self, v: ::protobuf::RepeatedField<RaftLogBackupFile>) {
        self.files = v;
    }

    // Mutable pointer to the field.
    pub fn mut_files(&mut self) -> &mut ::protobuf::RepeatedField<RaftLogBackupFile> {
        &mut self.files
    }

    // Take field
    pub fn take_files(&mut self) -> ::protobuf::RepeatedField<RaftLogBackupFile> {
        ::std::mem::replace(&mut self.files, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for KeySpaceBackupMeta {
    fn is_initialized(&self) -> bool {
        for v in &self.files {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.keyspace_id = tmp;
                },
                2 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.files)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.keyspace_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.keyspace_id, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.files {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.keyspace_id != 0 {
            os.write_uint32(1, self.keyspace_id)?;
        }
        for v in &self.files {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> KeySpaceBackupMeta {
        KeySpaceBackupMeta::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "keyspace_id",
                    |m: &KeySpaceBackupMeta| { &m.keyspace_id },
                    |m: &mut KeySpaceBackupMeta| { &mut m.keyspace_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RaftLogBackupFile>>(
                    "files",
                    |m: &KeySpaceBackupMeta| { &m.files },
                    |m: &mut KeySpaceBackupMeta| { &mut m.files },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<KeySpaceBackupMeta>(
                    "KeySpaceBackupMeta",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static KeySpaceBackupMeta {
        static mut instance: ::protobuf::lazy::Lazy<KeySpaceBackupMeta> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const KeySpaceBackupMeta,
        };
        unsafe {
            instance.get(KeySpaceBackupMeta::new)
        }
    }
}

impl ::protobuf::Clear for KeySpaceBackupMeta {
    fn clear(&mut self) {
        self.keyspace_id = 0;
        self.files.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for KeySpaceBackupMeta {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.keyspace_id, "keyspace_id", buf);
        ::protobuf::PbPrint::fmt(&self.files, "files", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for KeySpaceBackupMeta {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.keyspace_id, "keyspace_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.files, "files", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for KeySpaceBackupMeta {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct RaftLogMetaHeader {
    // message fields
    pub version: u64,
    pub compression_type: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a RaftLogMetaHeader {
    fn default() -> &'a RaftLogMetaHeader {
        <RaftLogMetaHeader as ::protobuf::Message>::default_instance()
    }
}

impl RaftLogMetaHeader {
    pub fn new() -> RaftLogMetaHeader {
        ::std::default::Default::default()
    }

    // uint64 version = 1;


    pub fn get_version(&self) -> u64 {
        self.version
    }
    pub fn clear_version(&mut self) {
        self.version = 0;
    }

    // Param is passed by value, moved
    pub fn set_version(&mut self, v: u64) {
        self.version = v;
    }

    // uint32 compression_type = 2;


    pub fn get_compression_type(&self) -> u32 {
        self.compression_type
    }
    pub fn clear_compression_type(&mut self) {
        self.compression_type = 0;
    }

    // Param is passed by value, moved
    pub fn set_compression_type(&mut self, v: u32) {
        self.compression_type = v;
    }
}

impl ::protobuf::Message for RaftLogMetaHeader {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.version = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.compression_type = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.version != 0 {
            my_size += ::protobuf::rt::value_size(1, self.version, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.compression_type != 0 {
            my_size += ::protobuf::rt::value_size(2, self.compression_type, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.version != 0 {
            os.write_uint64(1, self.version)?;
        }
        if self.compression_type != 0 {
            os.write_uint32(2, self.compression_type)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> RaftLogMetaHeader {
        RaftLogMetaHeader::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "version",
                    |m: &RaftLogMetaHeader| { &m.version },
                    |m: &mut RaftLogMetaHeader| { &mut m.version },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "compression_type",
                    |m: &RaftLogMetaHeader| { &m.compression_type },
                    |m: &mut RaftLogMetaHeader| { &mut m.compression_type },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<RaftLogMetaHeader>(
                    "RaftLogMetaHeader",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static RaftLogMetaHeader {
        static mut instance: ::protobuf::lazy::Lazy<RaftLogMetaHeader> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const RaftLogMetaHeader,
        };
        unsafe {
            instance.get(RaftLogMetaHeader::new)
        }
    }
}

impl ::protobuf::Clear for RaftLogMetaHeader {
    fn clear(&mut self) {
        self.version = 0;
        self.compression_type = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for RaftLogMetaHeader {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.version, "version", buf);
        ::protobuf::PbPrint::fmt(&self.compression_type, "compression_type", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for RaftLogMetaHeader {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.version, "version", &mut s);
        ::protobuf::PbPrint::fmt(&self.compression_type, "compression_type", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for RaftLogMetaHeader {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct StoreRaftLogBackupMeta {
    // message fields
    pub header: ::protobuf::SingularPtrField<RaftLogMetaHeader>,
    pub raft_logs: ::std::collections::HashMap<u32, KeySpaceBackupMeta>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a StoreRaftLogBackupMeta {
    fn default() -> &'a StoreRaftLogBackupMeta {
        <StoreRaftLogBackupMeta as ::protobuf::Message>::default_instance()
    }
}

impl StoreRaftLogBackupMeta {
    pub fn new() -> StoreRaftLogBackupMeta {
        ::std::default::Default::default()
    }

    // .rfpb.RaftLogMetaHeader header = 1;


    pub fn get_header(&self) -> &RaftLogMetaHeader {
        self.header.as_ref().unwrap_or_else(|| RaftLogMetaHeader::default_instance())
    }
    pub fn clear_header(&mut self) {
        self.header.clear();
    }

    pub fn has_header(&self) -> bool {
        self.header.is_some()
    }

    // Param is passed by value, moved
    pub fn set_header(&mut self, v: RaftLogMetaHeader) {
        self.header = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_header(&mut self) -> &mut RaftLogMetaHeader {
        if self.header.is_none() {
            self.header.set_default();
        }
        self.header.as_mut().unwrap()
    }

    // Take field
    pub fn take_header(&mut self) -> RaftLogMetaHeader {
        self.header.take().unwrap_or_else(|| RaftLogMetaHeader::new())
    }

    // repeated .rfpb.StoreRaftLogBackupMeta.raft_logs_MapEntry raft_logs = 2;


    pub fn get_raft_logs(&self) -> &::std::collections::HashMap<u32, KeySpaceBackupMeta> {
        &self.raft_logs
    }
    pub fn clear_raft_logs(&mut self) {
        self.raft_logs.clear();
    }

    // Param is passed by value, moved
    pub fn set_raft_logs(&mut self, v: ::std::collections::HashMap<u32, KeySpaceBackupMeta>) {
        self.raft_logs = v;
    }

    // Mutable pointer to the field.
    pub fn mut_raft_logs(&mut self) -> &mut ::std::collections::HashMap<u32, KeySpaceBackupMeta> {
        &mut self.raft_logs
    }

    // Take field
    pub fn take_raft_logs(&mut self) -> ::std::collections::HashMap<u32, KeySpaceBackupMeta> {
        ::std::mem::replace(&mut self.raft_logs, ::std::collections::HashMap::new())
    }
}

impl ::protobuf::Message for StoreRaftLogBackupMeta {
    fn is_initialized(&self) -> bool {
        for v in &self.header {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.header)?;
                },
                2 => {
                    ::protobuf::rt::read_map_into::<::protobuf::types::ProtobufTypeUint32, ::protobuf::types::ProtobufTypeMessage<KeySpaceBackupMeta>>(wire_type, is, &mut self.raft_logs)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.header.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        my_size += ::protobuf::rt::compute_map_size::<::protobuf::types::ProtobufTypeUint32, ::protobuf::types::ProtobufTypeMessage<KeySpaceBackupMeta>>(2, &self.raft_logs);
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.header.as_ref() {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        ::protobuf::rt::write_map_with_cached_sizes::<::protobuf::types::ProtobufTypeUint32, ::protobuf::types::ProtobufTypeMessage<KeySpaceBackupMeta>>(2, &self.raft_logs, os)?;
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> StoreRaftLogBackupMeta {
        StoreRaftLogBackupMeta::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<RaftLogMetaHeader>>(
                    "header",
                    |m: &StoreRaftLogBackupMeta| { &m.header },
                    |m: &mut StoreRaftLogBackupMeta| { &mut m.header },
                ));
                fields.push(::protobuf::reflect::accessor::make_map_accessor::<_, ::protobuf::types::ProtobufTypeUint32, ::protobuf::types::ProtobufTypeMessage<KeySpaceBackupMeta>>(
                    "raft_logs",
                    |m: &StoreRaftLogBackupMeta| { &m.raft_logs },
                    |m: &mut StoreRaftLogBackupMeta| { &mut m.raft_logs },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<StoreRaftLogBackupMeta>(
                    "StoreRaftLogBackupMeta",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static StoreRaftLogBackupMeta {
        static mut instance: ::protobuf::lazy::Lazy<StoreRaftLogBackupMeta> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const StoreRaftLogBackupMeta,
        };
        unsafe {
            instance.get(StoreRaftLogBackupMeta::new)
        }
    }
}

impl ::protobuf::Clear for StoreRaftLogBackupMeta {
    fn clear(&mut self) {
        self.header.clear();
        self.raft_logs.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for StoreRaftLogBackupMeta {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.header, "header", buf);
        ::protobuf::PbPrint::fmt(&self.raft_logs, "raft_logs", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for StoreRaftLogBackupMeta {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.header, "header", &mut s);
        ::protobuf::PbPrint::fmt(&self.raft_logs, "raft_logs", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for StoreRaftLogBackupMeta {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x0fchangeset.proto\x12\x04rfpb\"B\n\tChangeSet\x12\x12\n\x08epoch_id\
    \x18\x01\x20\x01(\rB\0\x12\x1f\n\x05peers\x18\x02\x20\x03(\x0b2\x0e.rfpb\
    .PeerMetaB\0:\0\"\x96\x01\n\x08PeerMeta\x12\x11\n\x07peer_id\x18\x01\x20\
    \x01(\x04B\0\x12\x13\n\tregion_id\x18\x02\x20\x01(\x04B\0\x12\x19\n\x0ft\
    runcated_index\x18\x03\x20\x01(\x04B\0\x12!\n\x06states\x18\x04\x20\x03(\
    \x0b2\x0f.rfpb.PeerStateB\0\x12\"\n\x05files\x18\x05\x20\x03(\x0b2\x11.r\
    fpb.RaftLogFileB\0:\0\"-\n\tPeerState\x12\r\n\x03key\x18\x01\x20\x01(\
    \x0cB\0\x12\x0f\n\x05value\x18\x02\x20\x01(\x0cB\0:\0\"<\n\x0bRaftLogFil\
    e\x12\x15\n\x0bfirst_index\x18\x01\x20\x01(\x04B\0\x12\x14\n\nlast_index\
    \x18\x02\x20\x01(\x04B\0:\0\"\xe8\x02\n\x0fStoreBackupMeta\x12\x12\n\x08\
    store_id\x18\x01\x20\x01(\x04B\0\x12#\n\x08manifest\x18\x02\x20\x01(\x0b\
    2\x0f.rfpb.ChangeSetB\0\x12$\n\nwal_chunks\x18\x03\x20\x03(\x0b2\x0e.rfp\
    b.WalChunkB\0\x12\x1d\n\x13raft_meta_start_off\x18\x04\x20\x01(\x04B\0\
    \x12\x0f\n\x05epoch\x18\x05\x20\x01(\rB\0\x12\x10\n\x06offset\x18\x06\
    \x20\x01(\x04B\0\x12E\n\rkeyspace_size\x18\x07\x20\x03(\x0b2,.rfpb.Store\
    BackupMeta.keyspace_size_MapEntryB\0\x12#\n\x19has_missing_commit_record\
    \x18\x08\x20\x01(\x08B\0\x1aF\n\x16keyspace_size_MapEntry\x12\t\n\x03key\
    \x18\x01(\r\x12\x1d\n\x05value\x18\x02(\x0b2\x10.rfpb.BackupSize:\x028\
    \x01:\0\"\x1e\n\nBackupSize\x12\x0e\n\x04size\x18\x01\x20\x01(\x04B\0:\0\
    \"E\n\x08WalChunk\x12\x0f\n\x05epoch\x18\x01\x20\x01(\rB\0\x12\x13\n\tst\
    art_off\x18\x02\x20\x01(\x04B\0\x12\x11\n\x07end_off\x18\x03\x20\x01(\
    \x04B\0:\0\"\xdb\x02\n\x11ClusterBackupMeta\x12'\n\x06stores\x18\x01\x20\
    \x03(\x0b2\x15.rfpb.StoreBackupMetaB\0\x12\x14\n\ncluster_id\x18\x02\x20\
    \x01(\x04B\0\x12\x13\n\tbackup_ts\x18\x03\x20\x01(\x04B\0\x12\x12\n\x08a\
    lloc_id\x18\x04\x20\x01(\x04B\0\x12\x11\n\x07safe_ts\x18\x05\x20\x01(\
    \x04B\0\x12G\n\rkeyspace_meta\x18\x06\x20\x03(\x0b2..rfpb.ClusterBackupM\
    eta.keyspace_meta_MapEntryB\0\x12\x17\n\rmeta_revision\x18\x07\x20\x01(\
    \x03B\0\x12\x18\n\x0eis_lightweight\x18\x08\x20\x01(\x08B\0\x12\x17\n\rt\
    olerated_err\x18\t\x20\x01(\rB\0\x1a4\n\x16keyspace_meta_MapEntry\x12\t\
    \n\x03key\x18\x01(\x0c\x12\x0b\n\x05value\x18\x02(\x0c:\x028\x01:\0\"}\n\
    \x11RaftLogBackupFile\x12\x11\n\x07peer_id\x18\x01\x20\x01(\x04B\0\x12\
    \x15\n\x0bfirst_index\x18\x02\x20\x01(\x04B\0\x12\x14\n\nlast_index\x18\
    \x03\x20\x01(\x04B\0\x12\x13\n\tstart_off\x18\x04\x20\x01(\x04B\0\x12\
    \x11\n\x07end_off\x18\x05\x20\x01(\x04B\0:\0\"W\n\x12KeySpaceBackupMeta\
    \x12\x15\n\x0bkeyspace_id\x18\x01\x20\x01(\rB\0\x12(\n\x05files\x18\x02\
    \x20\x03(\x0b2\x17.rfpb.RaftLogBackupFileB\0:\0\"D\n\x11RaftLogMetaHeade\
    r\x12\x11\n\x07version\x18\x01\x20\x01(\x04B\0\x12\x1a\n\x10compression_\
    type\x18\x02\x20\x01(\rB\0:\0\"\xd7\x01\n\x16StoreRaftLogBackupMeta\x12)\
    \n\x06header\x18\x01\x20\x01(\x0b2\x17.rfpb.RaftLogMetaHeaderB\0\x12D\n\
    \traft_logs\x18\x02\x20\x03(\x0b2/.rfpb.StoreRaftLogBackupMeta.raft_logs\
    _MapEntryB\0\x1aJ\n\x12raft_logs_MapEntry\x12\t\n\x03key\x18\x01(\r\x12%\
    \n\x05value\x18\x02(\x0b2\x18.rfpb.KeySpaceBackupMeta:\x028\x01:\0B\0b\
    \x06proto3\
";

static mut file_descriptor_proto_lazy: ::protobuf::lazy::Lazy<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::lazy::Lazy {
    lock: ::protobuf::lazy::ONCE_INIT,
    ptr: 0 as *const ::protobuf::descriptor::FileDescriptorProto,
};

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    unsafe {
        file_descriptor_proto_lazy.get(|| {
            parse_descriptor_proto()
        })
    }
}
