// Copyright 2022-present PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// See the License for the specific language governing permissions and
// limitations under the License.

// Use ./gen.sh to generate .pb.go files.
syntax = "proto3";

package rfpb;

message ChangeSet {
    uint32 epoch_id = 1;
    repeated PeerMeta peers = 2;
}

message PeerMeta {
    uint64 peer_id = 1;
    uint64 region_id = 2;
    uint64 truncated_index = 3;
    repeated PeerState states = 4;
    repeated RaftLogFile files = 5;
}

message PeerState {
    bytes key = 1;
    bytes value = 2;
}

message RaftLogFile {
    uint64 first_index = 1;
    uint64 last_index = 2;
}

message StoreBackupMeta {
    uint64 store_id = 1;
    ChangeSet manifest = 2;
    repeated WalChunk wal_chunks = 3;
    uint64 raft_meta_start_off = 4;
    // for lightweight backup, wal_chunks filed will be deleted in future
    uint32 epoch = 5;
    uint64 offset = 6;
    // The estimated size each keyspace used.
    map<uint32, BackupSize> keyspace_size = 7;
    bool has_missing_commit_record = 8;
}

message BackupSize {
    uint64 size = 1;
}

message WalChunk {
    uint32 epoch = 1;
    uint64 start_off = 2;
    uint64 end_off = 3;
}

message ClusterBackupMeta {
    repeated StoreBackupMeta stores = 1;
    uint64 cluster_id = 2;
    uint64 backup_ts = 3;
    uint64 alloc_id = 4;
    uint64 safe_ts = 5;
    // use map for update convenience
    map<bytes, bytes> keyspace_meta = 6;
    int64 meta_revision = 7;
    bool is_lightweight = 8;
    uint32 tolerated_err = 9;
}

message RaftLogBackupFile {
    uint64 peer_id = 1;
    uint64 first_index = 2;
    uint64 last_index = 3;
    uint64 start_off = 4;
    uint64 end_off = 5;
}

message KeySpaceBackupMeta {
    uint32 keyspace_id = 1;
    repeated RaftLogBackupFile files = 2;
}

message RaftLogMetaHeader {
    uint64 version = 1;
    // 0 -> No compression
    // 1 -> LZ4 compression
    uint32 compression_type = 2;
}

message StoreRaftLogBackupMeta {
    RaftLogMetaHeader header = 1;
    // keyspace_id -> KeySpaceBackupMeta
    map<uint32, KeySpaceBackupMeta> raft_logs = 2;
}