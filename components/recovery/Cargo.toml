[package]
name = "recovery"
version = "0.1.0"
edition = "2021"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
dashmap = "6.1.0"
http = "0.2"
hyper = "0.14"
lazy_static = "1.4.0"
security = { path = "../security", default-features = false }
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_ignored = "0.1"
serde_json = "1.0"
url = "2"
