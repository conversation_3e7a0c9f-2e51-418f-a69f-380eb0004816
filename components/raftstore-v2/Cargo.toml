[package]
name = "raftstore-v2"
version = "0.1.0"
edition = "2021"

[features]
default = ["testexport", "test-engine-kv-rocksdb", "test-engine-raft-raft-engine"]
failpoints = ["raftstore/failpoints"]
testexport = ["raftstore/testexport"]
test-engine-kv-rocksdb = [
  "raftstore/test-engine-kv-rocksdb",
  "engine_test/test-engine-kv-rocksdb",
]
test-engine-raft-raft-engine = [
  "raftstore/test-engine-raft-raft-engine",
  "engine_test/test-engine-raft-raft-engine",
]
test-engines-rocksdb = [
  "raftstore/test-engines-rocksdb",
  "engine_test/test-engines-rocksdb",
]
test-engines-panic = [
  "raftstore/test-engines-panic",
  "engine_test/test-engines-panic",
]

cloud-aws = ["raftstore/cloud-aws"]
cloud-gcp = ["raftstore/cloud-gcp"]
cloud-azure = ["raftstore/cloud-azure"]

[dependencies]
batch-system = { workspace = true }
causal_ts = { workspace = true }
collections = { workspace = true }
concurrency_manager = { workspace = true }
crossbeam = "0.8"
engine_traits = { workspace = true }
error_code = { workspace = true }
fail = "0.5"
file_system = { workspace = true }
fs2 = "0.4"
futures = { version = "0.3", features = ["compat"] }
keys = { workspace = true }
kvproto = { workspace = true }
log_wrappers = { workspace = true }
pd_client = { workspace = true }
prometheus = { version = "0.13", features = ["nightly"] }
protobuf = { version = "2.8", features = ["bytes"] }
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raft-proto = { version = "0.7.0" }
raftstore = { workspace = true }
resource_metering = { workspace = true }
slog = "2.3"
smallvec = "1.4"
tikv_util = { workspace = true }
time = "0.1"
tracker = { workspace = true }
txn_types = { workspace = true }
yatp = { git = "https://github.com/tikv/yatp.git", branch = "master" }

[dev-dependencies]
engine_test = { workspace = true }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tempfile = "3.0"
test_pd = { workspace = true }
test_util = { workspace = true }

[[test]]
name = "raftstore-v2-failpoints"
path = "tests/failpoints/mod.rs"
required-features = ["failpoints", "testexport"]

[[test]]
name = "raftstore-v2-integrations"
path = "tests/integrations/mod.rs"
required-features = ["testexport"]
