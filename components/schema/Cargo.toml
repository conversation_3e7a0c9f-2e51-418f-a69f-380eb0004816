[package]
name = "schema"
version = "0.1.0"
edition = "2021"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[features]
testexport = []

[dependencies]
api_version = { workspace = true }
async-trait = "0.1"
base64 = "0.13"
bytes = "1.0"
chrono = "0.4"
kvenginepb = { workspace = true }
log_wrappers = { workspace = true }
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_ignored = "0.1"
serde_json = "1.0"
serde_repr = "0.1"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog-term = "2.4"
tidb_query_datatype = { workspace = true }
tikv_util = { workspace = true }
tipb = { workspace = true }
