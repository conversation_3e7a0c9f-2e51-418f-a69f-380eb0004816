[package]
name = "rfengine"
version = "0.0.1"
authors = ["The TiKV Authors"]
license = "Apache-2.0"
edition = "2018"
publish = false

[features]
testexport = []

[dependencies]
api_version = { path = "../api_version" }
byteorder = "1.2"
bytes = "1.0"
chrono = "0.4"
crc32c = { workspace = true }
engine_traits = { path = "../engine_traits", default-features = false }
error_code = { path = "../error_code", default-features = false }
file_system = { path = "../file_system", default-features = false }
fslock = "0.1.7"
kvengine = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
libc = "0.2"
log_wrappers = { path = "../log_wrappers" }
lz4 = "1.24"
papaya = "0.2.3"
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
protobuf = "2.8"
quick_cache = "0.6.9"
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raft-proto = { version = "0.7.0", default-features = false }
regex = "1.9.5"
rfenginepb = { path = "../rfenginepb" }
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_json = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog-term = "2.4"
thiserror = "1.0"
tikv_alloc = { path = "../tikv_alloc" }
tikv_util = { path = "../tikv_util" }
tracker = { path = "../tracker" }

[dev-dependencies]
futures = "0.3"
rand = "0.8"
rstest = "0.18"
tempfile = "3.0"
test_util = { workspace = true }
