// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.
use std::{convert::Try<PERSON>rom, io::Write};

use anyhow::{anyhow, bail, Result};
use bytes::BufMut;
use kvenginepb::fts;
use protobuf::Message;
use std::convert::TryInto;
use xorf::BinaryFuse8;

use super::{calculate_checksum, Footer, HandleData, FOOTER_SIZE, HANDLE_BLOCK_TARGET_SIZE};

pub struct DedicatedFileBuilder<W: Write> {
    writer: W,
    offset: u64,
    properties: fts::PropertyBlock,

    // For streaming handle blocks
    handle_index: fts::HandleIndexBlock,
    current_handle_block: Vec<u8>,
    first_key_in_block: bool,
    last_key: Vec<u8>,

    pk_hashes: Vec<u64>,
}

impl<W: Write> DedicatedFileBuilder<W> {
    pub fn new(
        writer: W,
        lp_key: Vec<u8>,
        is_int_handle: bool,
    ) -> Self {
        let mut properties = fts::PropertyBlock::new();
        properties.set_lp_key(lp_key);
        properties.set_is_int_handle(is_int_handle);
        Self {
            writer,
            offset: 0,
            properties,
            handle_index: fts::HandleIndexBlock::new(),
            current_handle_block: Vec::with_capacity(HANDLE_BLOCK_TARGET_SIZE),
            first_key_in_block: true,
            last_key: Vec::new(),
            pk_hashes: Vec::new(),
        }
    }

    pub fn write_index_data(&mut self, data: &[u8]) -> Result<()> {
        if self.offset != 0 {
            bail!("write_index_data must be called first");
        }
        self.writer.write_all(data)?;
        self.offset += u64::try_from(data.len())?;
        Ok(())
    }

    pub fn add_handle(&mut self, key: &[u8], data: &HandleData) -> Result<()> {
        if !self.last_key.is_empty(){
            if self.properties.is_int_handle{
                let last_key = u64::from_be_bytes(self.last_key.as_slice().try_into()?);
                let key = u64::from_be_bytes(key.try_into()?);
                if key <= last_key {
                    bail!("Handles must be added in ascending order.");
                }
            }
            let last_key = self.last_key.as_slice();
            if key <= last_key {
                bail!("Handles must be added in ascending order. key is: {:?}, last_key is: {:?}",
                    key,
                    last_key
                );
            }
        }

        if self.first_key_in_block {
            if self.handle_index.handle_block_offset.is_empty() {
                self.handle_index.handle_block_offset.push(self.offset);
            }
            self.handle_index.handle_block_start_key.push(key.to_vec());
            self.first_key_in_block = false;
        }

        self.current_handle_block
            .put_u32_le(u32::try_from(key.len())?);
        self.current_handle_block.put_slice(key);
        self.current_handle_block
            .put_u32_le(u32::try_from(data.versions.len())?);
        for &v in &data.versions {
            self.current_handle_block.put_u64_le(v);
        }
        self.current_handle_block
            .put_u32_le(u32::try_from(data.delete_marks.len())?);
        self.current_handle_block.put_slice(&data.delete_marks);

        self.pk_hashes.push(farmhash::fingerprint64(key));
        self.last_key.clear();
        self.last_key.extend_from_slice(key);

        if self.current_handle_block.len() >= HANDLE_BLOCK_TARGET_SIZE {
            self.finish_handle_block()?;
        }
        Ok(())
    }

    fn finish_handle_block(&mut self) -> Result<()> {
        if self.current_handle_block.is_empty() {
            return Ok(());
        }

        let checksum = calculate_checksum(&self.current_handle_block);
        self.current_handle_block.put_u32_le(checksum);
        let padding = (8 - (self.current_handle_block.len() % 8)) % 8;
        self.current_handle_block.extend_from_slice(&vec![0; padding]);

        self.writer.write_all(&self.current_handle_block)?;
        self.offset += u64::try_from(self.current_handle_block.len())?;

        self.handle_index.handle_block_offset.push(self.offset);
        self.current_handle_block.clear();
        self.first_key_in_block = true;
        Ok(())
    }

    pub fn finish(mut self) -> Result<u64> {
        // Finish any pending handle block
        self.finish_handle_block()?;

        self.properties.set_files(fts::Files::new());
        let prop_bytes = self.properties.write_to_bytes()?;

        // Serialize the bloom filter
        let pk_filter_bytes = if !self.pk_hashes.is_empty() {
            let hashes: Vec<u64> = self.pk_hashes.into_iter().collect();
            let filter = BinaryFuse8::try_from(&hashes)
                .map_err(|e| anyhow!("Failed to build PK filter: {}", e))?;
            filter.to_vec()
        } else {
            Vec::new()
        };

        let handle_index_bytes = self.handle_index.write_to_bytes()?;

        let mut footer = Footer {
            format_version: 1,
            checksum_type: 1, // Crc32
            data_block_offset: 0,
            pk_filter_block_offset: 0,
            handle_index_block_offset: 0,
            property_block_offset: 0,
        };

        // The data block (tantivy index) is assumed to be already written at offset 0.
        footer.data_block_offset = 0;

        // Write PK filter
        footer.pk_filter_block_offset = self.offset;
        self.writer.write_all(&pk_filter_bytes)?;
        self.offset += u64::try_from(pk_filter_bytes.len())?;

        // Write handle index
        footer.handle_index_block_offset = self.offset;
        self.writer.write_all(&handle_index_bytes)?;
        self.offset += u64::try_from(handle_index_bytes.len())?;

        // Write properties
        footer.property_block_offset = self.offset;
        self.writer.write_all(&prop_bytes)?;
        self.offset += u64::try_from(prop_bytes.len())?;

        // Build and write footer
        let mut footer_buf = [0u8; FOOTER_SIZE];
        footer.encode(&mut footer_buf)?;

        let mut meta_buf = Vec::with_capacity(
            pk_filter_bytes.len() + handle_index_bytes.len() + prop_bytes.len(),
        );
        meta_buf.extend_from_slice(&pk_filter_bytes);
        meta_buf.extend_from_slice(&handle_index_bytes);
        meta_buf.extend_from_slice(&prop_bytes);
        let meta_checksum = calculate_checksum(&meta_buf);
        (&mut footer_buf[12..16]).put_u32_le(meta_checksum);

        let footer_checksum = calculate_checksum(&footer_buf[..8]);
        (&mut footer_buf[8..12]).put_u32_le(footer_checksum);

        self.writer.write_all(&footer_buf)?;
        self.offset += u64::try_from(footer_buf.len())?;

        Ok(self.offset)
    }
}