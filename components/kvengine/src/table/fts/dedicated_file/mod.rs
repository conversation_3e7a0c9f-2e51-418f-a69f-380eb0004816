// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

pub mod builder;
use std::sync::Arc;
use crate::table;

use anyhow::{bail, Result};
pub use builder::*;
use bytes::{Buf, BufMut};
use crc32fast::Hasher;
use bytes::Bytes;
use fastbloom::BloomFilter;
use kvenginepb::fts;
use once_cell::sync::OnceCell;

use crate::table::file::File;

pub const DEDICATED_FILE_MAGIC: u32 = 0xFDED1CA7;
pub const FOOTER_SIZE: usize = 52;
pub const HANDLE_BLOCK_TARGET_SIZE: usize = 1024 * 1024;

#[derive(Debug, PartialEq, Clone)]
pub struct Footer {
    pub format_version: u8,
    pub checksum_type: u8,
    pub data_block_offset: u64,
    pub pk_filter_block_offset: u64,
    pub handle_index_block_offset: u64,
    pub property_block_offset: u64,
}

impl Footer {
    pub fn encode(&self, buf: &mut [u8]) -> Result<()> {
        if buf.len() < FOOTER_SIZE {
            bail!(
                "Buffer is too small. Minimum size: {}, actual size: {}",
                FOOTER_SIZE,
                buf.len()
            );
        }
        let mut writer = &mut buf[..];
        writer.put_u8(self.format_version);
        writer.put_u8(self.checksum_type);
        writer.put(&[0u8; 6][..]);
        writer.put_u32_le(0);
        writer.put_u32_le(0);
        writer.put_u64_le(self.data_block_offset);
        writer.put_u64_le(self.pk_filter_block_offset);
        writer.put_u64_le(self.handle_index_block_offset);
        writer.put_u64_le(self.property_block_offset);
        writer.put_u32_le(DEDICATED_FILE_MAGIC);
        Ok(())
    }
    pub fn decode(buf: &[u8]) -> Result<Self> {
        if buf.len() < FOOTER_SIZE {
            bail!(
                "Buffer is too small. Minimum size: {}, actual size: {}",
                FOOTER_SIZE,
                buf.len()
            );
        }
        let mut reader = &buf[..];
        let s = Self {
            format_version: reader.get_u8(),
            checksum_type: reader.get_u8(),
            data_block_offset: {
                reader.advance(6);
                let _ = reader.get_u32_le();
                let _ = reader.get_u32_le();
                reader.get_u64_le()
            },
            pk_filter_block_offset: reader.get_u64_le(),
            handle_index_block_offset: reader.get_u64_le(),
            property_block_offset: reader.get_u64_le(),
        };
        if reader.get_u32_le() != DEDICATED_FILE_MAGIC {
            bail!(
                "Invalid magic number. Expected: {:#X}, found: {:#X}",
                DEDICATED_FILE_MAGIC,
                reader.get_u32()
            );
        }
        Ok(s)
    }
}

#[derive(Debug, Clone, PartialEq)]
pub struct HandleData {
    pub versions: Vec<u64>,
    pub delete_marks: Vec<u8>,
}

pub struct DedicatedFileReader {
    file: Arc<dyn File>,
    footer: Footer,
    pk_filter_cache: OnceCell<BloomFilter>,
    handle_index_cache: OnceCell<fts::HandleIndexBlock>,
    properties_cache: OnceCell<fts::PropertyBlock>,
}

impl DedicatedFileReader {
    pub fn new(file: Arc<dyn File>) -> Result<Self> {
        if file.size() < FOOTER_SIZE as u64 {
            bail!(
                "File is too small. Minimum size: {}, actual size: {}",
                FOOTER_SIZE,
                file.size()
            );
        }
        let footer_offset = file.size() - FOOTER_SIZE as u64;
        let footer_buf = file.read(footer_offset, FOOTER_SIZE)?;
        let expected_footer_checksum = calculate_checksum(&footer_buf[..8]);
        if (&footer_buf[8..12]).get_u32_le() != expected_footer_checksum {
            bail!(
                "Footer checksum mismatch. Expected: {}, found: {}",
                expected_footer_checksum,
                (&footer_buf[8..12]).get_u32_le()
            );
        }
        let footer = Footer::decode(&footer_buf)?;
        let meta_end = footer_offset;
        let meta_start = footer.pk_filter_block_offset;
        if meta_end < meta_start {
            bail!(
                "File is too small. Minimum size: {}, actual size: {}",
                meta_start,
                meta_end
            );
        }
        let meta_buf = file.read(meta_start, (meta_end - meta_start) as usize)?;
        let expected_meta_checksum = calculate_checksum(&meta_buf);
        if (&footer_buf[12..16]).get_u32_le() != expected_meta_checksum {
            bail!(
                "Metadata checksum mismatch. Expected: {}, found: {}",
                expected_meta_checksum,
                (&footer_buf[12..16]).get_u32_le()
            );
        }
        Ok(Self {
            file,
            footer,
            pk_filter_cache: OnceCell::new(),
            handle_index_cache: OnceCell::new(),
            properties_cache: OnceCell::new(),
        })
    }

    pub fn footer(&self) -> &Footer {
        &self.footer
    }

    pub fn properties(&self) -> Result<&fts::PropertyBlock> {
        self.properties_cache.get_or_try_init(|| {
            let offset = self.footer.property_block_offset as u64;
            let len = self.file.size() - offset - (FOOTER_SIZE as u64);
            let data = self.file.read(offset, len as usize)?;
            Ok(protobuf::parse_from_bytes::<fts::PropertyBlock>(&data)?)
        })
    }

    pub fn handle_index(&self) -> Result<&fts::HandleIndexBlock> {
        self.handle_index_cache.get_or_try_init(|| {
            let offset = self.footer.handle_index_block_offset as u64;
            let len = self.footer.property_block_offset as u64 - offset;
            let data = self.file.read(offset, len as usize)?;
            Ok(protobuf::parse_from_bytes::<fts::HandleIndexBlock>(&data)?)
        })
    }

    fn pk_filter_bytes(&self) -> table::Result<Bytes> {
        let offset = self.footer.pk_filter_block_offset as u64;
        let len = self.footer.handle_index_block_offset as u64 - offset;
        self.file.read(offset, len as usize)
    }

    pub fn check_pk_filter(&self, key: &[u8]) -> Result<bool> {
        let filter = self.pk_filter_cache.get_or_try_init(|| {
            let bytes = self.pk_filter_bytes()?;
            if bytes.is_empty() {
                return Ok(BloomFilter::with_num_bits(8).expected_items(0));
            }
            bincode::deserialize(&bytes).map_err(|e| anyhow::anyhow!(e))
        })?;
        Ok(filter.contains(key))
    }

    pub fn contains_handle(&self, key: &[u8]) -> Result<bool> {
        if !self.check_pk_filter(key)? {
            return Ok(false);
        }
        let handle_index = self.handle_index()?;
        if handle_index.handle_block_start_key.is_empty() {
            return Ok(false);
        }
        let block_idx = handle_index
            .handle_block_start_key
            .partition_point(|sk| sk.as_slice() <= key)
            .saturating_sub(1);
        if key < handle_index.handle_block_start_key[block_idx].as_slice() {
            return Ok(false);
        }
        let offsets = &handle_index.handle_block_offset;
        if block_idx + 1 >= offsets.len() {
            bail!("Handle index is malformed");
        }

        let block_start = offsets[block_idx];
        let block_end = offsets[block_idx + 1];
        let block_data_bytes = self.file.read(block_start, (block_end - block_start) as usize)?;
        let mut block_data = block_data_bytes.as_ref();

        while block_data.has_remaining() && block_data.remaining() > 8 {
            let key_len = block_data.get_u32_le() as usize;
            if block_data.remaining() < key_len {
                break;
            }
            let current_key = &block_data[..key_len];
            block_data.advance(key_len);
            if current_key == key {
                return Ok(true);
            }
            let versions_len = block_data.get_u32_le() as usize;
            block_data.advance(versions_len * 8);
            let deletes_len = block_data.get_u32_le() as usize;
            block_data.advance(deletes_len);
            if block_data.remaining() == 0 {
                break;
            }
        }
        Ok(false)
    }
}

fn calculate_checksum(data: &[u8]) -> u32 {
    let mut hasher = Hasher::new();
    hasher.update(data);
    hasher.finalize()
}

#[cfg(test)]
mod tests {

    use super::*;
    fn make_common_handle_key(i: u32) -> Vec<u8> {
        format!("handle_{:04}", i).into_bytes()
    }
    fn make_handle_data(i: u32) -> HandleData {
        HandleData {
            versions: vec![i as u64, (i + 1) as u64],
            delete_marks: vec![(i % 2) as u8],
        }
    }

    // Helper function for int handle tests
    fn make_int_handle_key(i: u64) -> Vec<u8> {
        i.to_be_bytes().to_vec()
    }

    #[test]
    fn test_roundtrip_happy_path() {
        let mut buffer = Vec::new();
        let mut builder = DedicatedFileBuilder::new(
            &mut buffer,
            b"lp_1".to_vec(),
            false,
            0.01,
            10, // expected_handles
        );
        builder.write_index_data(b"tantivy_index_goes_here").unwrap();
        builder.add_handle(&make_common_handle_key(1), &make_handle_data(1)).unwrap();
        builder.add_handle(&make_common_handle_key(10), &make_handle_data(10)).unwrap();
        builder.add_handle(&make_common_handle_key(5), &make_handle_data(5)).unwrap_err(); // Order check
        builder.add_handle(&make_common_handle_key(15), &make_handle_data(15)).unwrap();
        builder.finish().unwrap();

        let file = Arc::new(crate::table::file::InMemFile::new(1, buffer.into()));
        let reader = DedicatedFileReader::new(file).unwrap();
        let props = reader.properties().unwrap();
        assert_eq!(props.get_lp_key(), b"lp_1");
        assert!(reader.contains_handle(&make_common_handle_key(1)).unwrap());
        assert!(!reader.contains_handle(&make_common_handle_key(5)).unwrap());
        assert!(reader.contains_handle(&make_common_handle_key(10)).unwrap());
        assert!(reader.contains_handle(&make_common_handle_key(15)).unwrap());
        assert!(!reader.contains_handle(&make_common_handle_key(99)).unwrap());
    }

    #[test]
    fn test_pk_filter_logic() {
        let mut buffer = Vec::new();
        let key_count = 1000;
        let mut builder = DedicatedFileBuilder::new(
            &mut buffer,
            b"lp_filter_test".to_vec(),
            false,
            0.0001,
            key_count,
        );
        builder.write_index_data(b"index").unwrap();
        for i in 0..key_count as u32 {
            builder.add_handle(&make_common_handle_key(i), &make_handle_data(i)).unwrap();
        }
        builder.finish().unwrap();

        let file = Arc::new(crate::table::file::InMemFile::new(1, buffer.into()));
        let reader = DedicatedFileReader::new(file).unwrap();
        for i in 0..key_count as u32 {
            assert!(reader.check_pk_filter(&make_common_handle_key(i)).unwrap());
            assert!(reader.contains_handle(&make_common_handle_key(i)).unwrap());
        }
        let mut false_positives = 0;
        let test_count = 10000;
        for i in (key_count as u32)..(key_count as u32 + test_count) {
            let non_existent_key = make_common_handle_key(i);
            assert!(!reader.contains_handle(&non_existent_key).unwrap());
            if reader.check_pk_filter(&non_existent_key).unwrap() {
                false_positives += 1;
            }
        }
        let fp_rate = false_positives as f64 / test_count as f64;
        println!("False positive rate: {}", fp_rate);
        assert!(fp_rate < 0.01, "False positive rate is too high!");
    }

    #[test]
    fn test_roundtrip_empty_handles() {
        let mut buffer = Vec::new();
        let builder = DedicatedFileBuilder::new(
            &mut buffer,
            b"lp_2".to_vec(),
            true,
            0.01,
            0, // expected_handles
        );
        builder.finish().unwrap();

        let file = Arc::new(crate::table::file::InMemFile::new(1, buffer.into()));
        let reader = DedicatedFileReader::new(file).unwrap();
        assert_eq!(reader.properties().unwrap().get_lp_key(), b"lp_2");
        assert!(!reader.check_pk_filter(b"any_handle").unwrap());
        assert!(!reader.contains_handle(b"any_handle").unwrap());
        assert!(
            reader
                .handle_index()
                .unwrap()
                .handle_block_start_key
                .is_empty()
        );
    }

    #[test]
    fn test_single_common_handle_block() {
        let mut builder = DedicatedFileBuilder::new(b"lp_single_block".to_vec(), false, 0.01);

        // Add 1000 handles, total size is much less than 1MB, ensuring only one block
        for i in 0..1000 {
            builder.add_handle(make_common_handle_key(i), make_handle_data(i));
        }

        let buffer = builder.build().unwrap();
        let reader = DedicatedFileReader::new(&buffer).unwrap();

        // Verify the structure of the index block
        let handle_index = reader.handle_index().unwrap();
        assert_eq!(
            handle_index.handle_block_start_key.len(),
            1,
            "Should have exactly one handle block start key"
        );
        assert_eq!(
            handle_index.handle_block_offset.len(),
            2,
            "Should have start and end offsets for one block"
        );

        // Verify the data
        assert!(
            reader.contains_handle(&make_common_handle_key(0)).unwrap(),
            "Should find first key"
        );
        assert!(
            reader
                .contains_handle(&make_common_handle_key(500))
                .unwrap(),
            "Should find a middle key"
        );
        assert!(
            reader
                .contains_handle(&make_common_handle_key(999))
                .unwrap(),
            "Should find last key"
        );
        assert!(
            !reader
                .contains_handle(&make_common_handle_key(1000))
                .unwrap(),
            "Should not find a non-existent key"
        );
    }

    #[test]
    fn test_multiple_common_handle_blocks() {
        let mut builder = DedicatedFileBuilder::new(b"lp_multi_block".to_vec(), false, 0.01);

        // One handle is about 40 bytes. To create more than 2MB of data (to guarantee
        // at least 3 blocks), we need 2 * 1024 * 1024 / 40 = ~52428 handles. We
        // use 60000 to be sure.
        let key_count = 60000;
        for i in 0..key_count {
            builder.add_handle(make_common_handle_key(i), make_handle_data(i));
        }

        let buffer = builder.build().unwrap();
        let reader = DedicatedFileReader::new(&buffer).unwrap();

        // Verify the structure of the index block
        let handle_index = reader.handle_index().unwrap();
        assert!(
            handle_index.handle_block_start_key.len() > 1,
            "Should have multiple handle block start keys"
        );
        assert_eq!(
            handle_index.handle_block_start_key.len() + 1,
            handle_index.handle_block_offset.len(),
            "Offsets count should be keys count + 1"
        );

        // --- Core verification: searching across blocks ---

        // 1. Find the first and last elements of the first block
        let first_key_of_block0 = make_common_handle_key(0);
        assert_eq!(
            handle_index.handle_block_start_key[0], first_key_of_block0,
            "First block should start with key 0"
        );
        assert!(
            reader.contains_handle(&first_key_of_block0).unwrap(),
            "Should find the very first key"
        );

        // 2. Find the boundary of the second block
        let first_key_of_block1 = handle_index.handle_block_start_key[1].clone();

        // In Rust, the key "handle_xxxx" converted from an integer `i` is
        // lexicographically ordered. So we can find the adjacent key by parsing
        // the key.
        let key_string = String::from_utf8(first_key_of_block1.clone()).unwrap();
        let key_num: u32 = key_string.split('_').nth(1).unwrap().parse().unwrap();
        let key_just_before_block1 = make_common_handle_key(key_num - 1);

        assert!(
            reader.contains_handle(&first_key_of_block1).unwrap(),
            "Should find the first key of the second block"
        );
        assert!(
            reader.contains_handle(&key_just_before_block1).unwrap(),
            "Should find the key just before the second block (in the first block)"
        );

        // 3. Find the last element of the last block
        let last_key = make_common_handle_key(key_count - 1);
        assert!(
            reader.contains_handle(&last_key).unwrap(),
            "Should find the very last key"
        );

        // 4. Find a non-existent key
        let non_existent_key = make_common_handle_key(key_count);
        assert!(
            !reader.contains_handle(&non_existent_key).unwrap(),
            "Should not find a key outside the total range"
        );
    }

    #[test]
    fn test_int_handle_metadata_flag() {
        // This test only verifies that the is_int_handle flag is stored and read
        // correctly
        let mut builder = DedicatedFileBuilder::new(b"lp_int_handle_meta".to_vec(), true, 0.01);

        // We can use a u64 to represent an int handle, then convert it to bytes
        let int_handle_key = 1234567890_u64.to_be_bytes().to_vec();
        builder.add_handle(int_handle_key.clone(), make_handle_data(1));

        let buffer = builder.build().unwrap();
        let reader = DedicatedFileReader::new(&buffer).unwrap();

        // Core assertion: verify metadata
        let props = reader.properties().unwrap();
        assert!(
            props.get_is_int_handle(),
            "is_int_handle flag should be true"
        );

        // Also, verify that the data can still be found
        assert!(reader.contains_handle(&int_handle_key).unwrap());
    }

    #[test]
    fn test_single_int_handle_block() {
        let mut builder = DedicatedFileBuilder::new(b"lp_single_int_block".to_vec(), true, 0.01);

        // A u64 handle is 8 bytes, plus data, an entry is about 30 bytes.
        // 1000 entries (~30KB) is much smaller than 1MB, ensuring only one block.
        for i in 0..1000_u64 {
            builder.add_handle(make_int_handle_key(i), make_handle_data(i as u32));
        }

        let buffer = builder.build().unwrap();
        let reader = DedicatedFileReader::new(&buffer).unwrap();

        // Verify the structure of the index block
        let handle_index = reader.handle_index().unwrap();
        assert_eq!(
            handle_index.handle_block_start_key.len(),
            1,
            "Should have exactly one int handle block"
        );
        assert_eq!(
            handle_index.handle_block_offset.len(),
            2,
            "Should have start and end offsets for one block"
        );

        // Verify the data
        assert!(
            reader.contains_handle(&make_int_handle_key(0)).unwrap(),
            "Should find first int key"
        );
        assert!(
            reader.contains_handle(&make_int_handle_key(500)).unwrap(),
            "Should find a middle int key"
        );
        assert!(
            reader.contains_handle(&make_int_handle_key(999)).unwrap(),
            "Should find last int key"
        );
        assert!(
            !reader.contains_handle(&make_int_handle_key(1000)).unwrap(),
            "Should not find a non-existent int key"
        );
    }

    #[test]
    fn test_multiple_int_handle_blocks() {
        let mut builder = DedicatedFileBuilder::new(b"lp_multi_int_block".to_vec(), true, 0.01);

        // An int handle entry is about 30-40 bytes.
        // 70000 entries will produce more than 2MB of data, enough to create multiple
        // blocks.
        let key_count = 70000_u64;
        for i in 0..key_count {
            builder.add_handle(make_int_handle_key(i), make_handle_data(i as u32));
        }

        let buffer = builder.build().unwrap();
        let reader = DedicatedFileReader::new(&buffer).unwrap();

        // Verify the structure of the index block
        let handle_index = reader.handle_index().unwrap();
        assert!(
            handle_index.handle_block_start_key.len() > 1,
            "Should have multiple int handle blocks"
        );
        assert_eq!(
            handle_index.handle_block_start_key.len() + 1,
            handle_index.handle_block_offset.len(),
            "Offsets count should be keys count + 1"
        );

        // --- Core verification: searching across blocks ---

        // 1. Find the first element of the first block
        let first_key = make_int_handle_key(0);
        assert_eq!(
            handle_index.handle_block_start_key[0], first_key,
            "First block should start with key 0"
        );
        assert!(
            reader.contains_handle(&first_key).unwrap(),
            "Should find the very first int key"
        );

        // 2. Find the boundary of the second block
        // `to_be_bytes()` (Big-Endian) ensures that the byte order is consistent with
        // the numerical order.
        let first_key_of_block1_bytes = &handle_index.handle_block_start_key[1];
        let key_num_block1 =
            u64::from_be_bytes(first_key_of_block1_bytes.as_slice().try_into().unwrap());
        let key_just_before_block1 = make_int_handle_key(key_num_block1 - 1);

        assert!(
            reader.contains_handle(first_key_of_block1_bytes).unwrap(),
            "Should find the first key of the second block"
        );
        assert!(
            reader.contains_handle(&key_just_before_block1).unwrap(),
            "Should find the key just before the second block"
        );

        // 3. Find the last element of the last block
        let last_key = make_int_handle_key(key_count - 1);
        assert!(
            reader.contains_handle(&last_key).unwrap(),
            "Should find the very last key"
        );

        // 4. Find a non-existent key
        let non_existent_key = make_int_handle_key(key_count);
        assert!(
            !reader.contains_handle(&non_existent_key).unwrap(),
            "Should not find a key outside the total range"
        );
    }
}