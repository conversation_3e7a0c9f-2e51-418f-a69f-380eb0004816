// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::{
    fmt::Debug,
    sync::{Arc, RwLock},
};

use anyhow::{anyhow, bail, Result};
use byteorder::{Byte<PERSON>rde<PERSON>, <PERSON><PERSON><PERSON><PERSON>};
use bytes::{Buf, Bytes};
use hexhex::hex;
use kvenginepb::fts as ftspb;
use protobuf::Message;
use xorf::{BinaryFuse8, Filter};

use crate::{
    codecutil::{next_aligned_offset, BytesExt},
    ia::ia_file::{IaFile, IaMmapSource},
    table::{
        file::{File, TtlCache},
        ChecksumType,
    },
};

mod builder;

pub use builder::*;

/// Magic number for FTS PackedFile format
pub const FTS_PACKED_FILE_MAGIC: u32 = 0xFE75F11E;

/// Format version for FTS PackedFile
pub const FTS_PACKED_FILE_FORMAT_V1: u8 = 0x1;

/// Size of the serialized footer in bytes
pub const FTS_PACKED_FILE_FOOTER_SIZE: usize = 32;

/// Footer has a fixed size and is always located at the end of the file.
/// Footer size cannot be changed.
/// Format:
/// - 1 byte: format version
/// - 1 byte: checksum type
/// - 6 bytes: reserved
/// - 4 bytes: checksum footer
///            > Calculated as the checksum of the whole footer (with checksum
///            > footer set to 0)
/// - 4 bytes: checksum (remaining meta)
/// - 4 bytes: index block offset
/// - 4 bytes: lp (LogicalPartition) filter block offset
/// - 4 bytes: property block offset
/// - 4 bytes: magic number
#[repr(C)]
#[derive(Default, Clone, Copy, Debug)]
pub struct PackedFileFooter {
    pub format: u8,
    pub checksum_type: ChecksumType,
    pub checksum_other_meta: u32,
    pub index_block_offset: u32,
    pub lp_filter_block_offset: u32,
    pub prop_offset: u32,
    pub magic: u32,
}

impl PackedFileFooter {
    /// Get the size of the footer
    pub const fn footer_size() -> usize {
        FTS_PACKED_FILE_FOOTER_SIZE
    }

    /// Create a new footer with default values
    pub fn new() -> Self {
        Self {
            format: FTS_PACKED_FILE_FORMAT_V1,
            checksum_type: ChecksumType::None,
            checksum_other_meta: 0,
            index_block_offset: 0,
            lp_filter_block_offset: 0,
            prop_offset: 0,
            magic: FTS_PACKED_FILE_MAGIC,
        }
    }

    /// Unmarshal footer from bytes
    pub fn unmarshal(mut data: &[u8]) -> Result<Self> {
        if data.len() != FTS_PACKED_FILE_FOOTER_SIZE {
            bail!(
                "Invalid footer size, expected {:#x}, got {:#x}",
                FTS_PACKED_FILE_FOOTER_SIZE,
                data.len()
            );
        }
        let mut copied_data = [0u8; FTS_PACKED_FILE_FOOTER_SIZE];
        copied_data.copy_from_slice(data);

        let format = data.get_u8();
        let checksum_type_value = data.get_u8();
        let checksum_type = ChecksumType::from(checksum_type_value);
        data.advance(6);
        let checksum_footer = data.get_u32_le();
        let checksum_other_meta = data.get_u32_le();
        let index_block_offset = data.get_u32_le();
        let lp_filter_block_offset = data.get_u32_le();
        let prop_offset = data.get_u32_le();
        let magic = data.get_u32_le();
        if magic != FTS_PACKED_FILE_MAGIC {
            bail!(
                "Invalid magic number, expected {:#x}, got {:#x}",
                FTS_PACKED_FILE_MAGIC,
                magic
            );
        }
        if format != FTS_PACKED_FILE_FORMAT_V1 {
            bail!(
                "Unsupported format version, expected {:#x}, got {:#x}",
                FTS_PACKED_FILE_FORMAT_V1,
                format
            );
        }

        {
            // Verify footer's checksum
            copied_data[8..12].fill(0);
            let checksum_footer_actual = checksum_type.checksum(&copied_data);
            if checksum_footer != checksum_footer_actual {
                copied_data[8..12].copy_from_slice(&checksum_footer.to_le_bytes());
                bail!(
                    "FtsPackedFile footer checksum mismatch, actual {:#08x}, expect {:#08x}, footer data: {}, ChecksumType={:?}",
                    checksum_footer_actual,
                    checksum_footer,
                    hex(&copied_data),
                    checksum_type,
                );
            }
        }

        Ok(Self {
            format,
            checksum_type,
            checksum_other_meta,
            index_block_offset,
            lp_filter_block_offset,
            prop_offset,
            magic,
        })
    }

    /// Marshal footer to bytes
    pub fn marshal<W: std::io::Write>(&self, mut w: W) -> Result<usize> {
        use bytes::BufMut;

        let mut footer = [0u8; FTS_PACKED_FILE_FOOTER_SIZE];
        let mut data = &mut footer[..];
        data.put_u8(self.format);
        data.put_u8(self.checksum_type.value());
        data.put_slice(&[0; 6]); // Reserved bytes
        data.put_slice(&[0; 4]); // Placeholder for checksum footer
        data.put_u32_le(self.checksum_other_meta);
        data.put_u32_le(self.index_block_offset);
        data.put_u32_le(self.lp_filter_block_offset);
        data.put_u32_le(self.prop_offset);
        data.put_u32_le(self.magic);

        let checksum_footer = self.checksum_type.checksum(&footer);
        footer[8..12].copy_from_slice(&checksum_footer.to_le_bytes());

        w.write_all(&footer)?;

        Ok(FTS_PACKED_FILE_FOOTER_SIZE)
    }

    /// Returns the starting offset of the metadata blocks in the PackedFile
    /// indicated by this footer.
    ///
    /// As undex Block is the first metadata block, its offset is returned.
    pub fn metadata_offset(&self) -> u64 {
        self.index_block_offset as u64
    }
}

/// A FtsPackedFile in memory. Only minimal metadata is kept in memory.
/// The main data is accessed through mmap and acceleration data is cached via
/// TtlCache.
///
/// FtsPackedFile layout:
/// - Data blocks:     Each data block contains several LP entries
///                    > Data block is aligned to 8 bytes, paddings may be added
///                    > at the *beginning*
/// - Index block:     For quickly seeking a data block
/// - LP filter block: For checking whether LPKey must not exist
/// - Property block:  Some additional properties (in Protobuf)
/// - Footer:          Metadata
#[derive(Clone)]
pub struct PackedFile<Info>(Arc<PackedFileCore<Info>>);

/// Core implementation of PackedFile
struct PackedFileCore<Info> {
    file: Arc<IaFile>,

    index_block: TtlCache<ftspb::PackedFileIndexBlock>,
    lp_filter: TtlCache<BinaryFuse8>,
    props: ftspb::PackedFilePropertyBlock,
    footer: PackedFileFooter,
    data_block_checked: RwLock<Vec<bool>>,

    additional_info: Info,
}

impl PackedFile<()> {
    /// Create a new PackedFile from a remote file.
    pub fn new(file: Arc<IaFile>) -> Result<PackedFile<()>> {
        PackedFile::<()>::new_with_info(file, ())
    }

    /// Calculate IA segment offsets for an FTS packed file based on data
    /// blocks. This function reads the index block and calculates segment
    /// boundaries based on the standard IA segment size
    pub fn generate_ia_segment_boundaries(file: &IaFile, segment_size: u64) -> Result<Vec<u64>> {
        let footer = Self::load_footer(file)?;
        let index_block = Self::load_index_block(file, &footer)?;

        let mut segment_offsets = vec![0]; // Always start with offset 0
        let mut current_segment_start = 0u64;

        // Iterate through data block offsets and create segments when accumulated size
        // reaches the standard IA segment size
        for &block_offset in &index_block.data_block_offsets {
            let block_offset = block_offset as u64;
            // If this block would make the current segment exceed the segment size,
            // start a new segment
            if block_offset >= current_segment_start + segment_size {
                segment_offsets.push(block_offset);
                current_segment_start = block_offset;
            }
        }

        Ok(segment_offsets)
    }

    /// Load the footer from the packed file.
    fn load_footer(file: &IaFile) -> Result<PackedFileFooter> {
        let footer_data = file.read_footer(FTS_PACKED_FILE_FOOTER_SIZE)?;
        let footer = PackedFileFooter::unmarshal(footer_data.as_ref())?;
        Self::verify_meta_checksum(file, &footer)?;
        Ok(footer)
    }

    /// Load the properties block from the packed file.
    fn load_props(
        file: &IaFile,
        footer: &PackedFileFooter,
    ) -> Result<ftspb::PackedFilePropertyBlock> {
        let prop_offset = footer.prop_offset as u64;
        let prop_len = file
            .size()
            .checked_sub(FTS_PACKED_FILE_FOOTER_SIZE as u64 + prop_offset)
            .ok_or_else(|| {
                anyhow!(
                    "Property block offset {} is out of bounds for file size {}",
                    prop_offset,
                    file.size()
                )
            })? as usize;
        let mut prop = ftspb::PackedFilePropertyBlock::new();
        if prop_len > 0 {
            let data = file.read_table_meta(prop_offset, prop_len)?;
            prop.merge_from_bytes(&data)?;
        }
        Ok(prop)
    }

    /// Verify the metadata checksum for the packed file.
    fn verify_meta_checksum(file: &IaFile, footer: &PackedFileFooter) -> Result<()> {
        let offset = footer.metadata_offset();
        // The size should be the metadata size minus the footer size
        // file.size() = table_meta_off + meta_size, so meta_size = file.size() -
        // table_meta_off
        let meta_size = file.size() - file.table_meta_off;
        let size = meta_size as usize - FTS_PACKED_FILE_FOOTER_SIZE;
        let rest_meta_data = file.read_table_meta(offset, size)?;
        let checksum_actual = footer.checksum_type.checksum(&rest_meta_data);
        if checksum_actual != footer.checksum_other_meta {
            bail!(
                "FtsPackedFile meta checksum mismatch, actual {:#08x}, expect {:#08x}, ChecksumType={:?}",
                checksum_actual,
                footer.checksum_other_meta,
                footer.checksum_type
            );
        }
        Ok(())
    }

    /// Load the index block from the packed file.
    fn load_index_block(
        file: &IaFile,
        footer: &PackedFileFooter,
    ) -> Result<ftspb::PackedFileIndexBlock> {
        let offset = footer.index_block_offset as u64;
        let length = footer.lp_filter_block_offset - footer.index_block_offset;
        let data = file.read_table_meta(offset, length as usize)?;

        let mut index_block = ftspb::PackedFileIndexBlock::new();
        index_block.merge_from_bytes(&data)?;

        if index_block.data_block_offsets.is_empty() {
            bail!("Index block broken: data_block_offsets must not be empty");
        }
        if index_block.data_block_start_keys.len() + 1 != index_block.data_block_offsets.len() {
            bail!(
                "Index block broken: data_block_start_keys.len={} but data_block_offsets.len={}",
                index_block.data_block_start_keys.len(),
                index_block.data_block_offsets.len()
            );
        }

        Ok(index_block)
    }

    /// Load the LP filter block from the packed file.
    fn load_lp_filter(file: &IaFile, footer: &PackedFileFooter) -> Result<BinaryFuse8> {
        let offset = footer.lp_filter_block_offset as u64;
        let length = footer.prop_offset - footer.lp_filter_block_offset;
        let data = file.read_table_meta(offset, length as usize)?;

        let filter =
            BinaryFuse8::try_from_bytes(&data).map_err(|e| anyhow!("Bad LPKey filter: {}", e))?;
        Ok(filter)
    }
}

impl<Info: Debug> Debug for PackedFile<Info> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("PackedFile")
            .field("file", &self.0.file)
            .field("props", &self.0.props)
            .field("info", &self.0.additional_info)
            .finish()
    }
}

impl<Info> PackedFile<Info> {
    pub fn new_with_info(file: Arc<IaFile>, additional_info: Info) -> Result<PackedFile<Info>> {
        if file.is_sync() {
            // Currently local file is not supported.
            bail!(
                "FtsPackedFile can be only accessed via IaFile, got {:?}",
                file.path()
            );
        }

        let footer = PackedFile::load_footer(file.as_ref())?;
        let props = PackedFile::load_props(file.as_ref(), &footer)?;
        Ok(Self(Arc::new(PackedFileCore::<_> {
            file,
            index_block: TtlCache::default(),
            lp_filter: TtlCache::default(),
            props,
            footer,
            data_block_checked: RwLock::new(vec![]),

            additional_info,
        })))
    }

    /// Get or load the index block.
    /// Index block can be used to quickly find which data block contains the
    /// given logical partition key.
    fn get_index_block(&self) -> Result<Arc<ftspb::PackedFileIndexBlock>> {
        self.0
            .index_block
            .get(|| PackedFile::load_index_block(&self.0.file, &self.0.footer))
    }

    /// Get or load the LogicalPartition filter.
    /// LogicalPartition filter can be used to quickly check whether this packed
    /// file contains a logical partition key.
    fn get_lp_filter(&self) -> Result<Arc<BinaryFuse8>> {
        self.0
            .lp_filter
            .get(|| PackedFile::load_lp_filter(&self.0.file, &self.0.footer))
    }

    /// Returns the property block.
    pub fn props(&self) -> &ftspb::PackedFilePropertyBlock {
        &self.0.props
    }

    /// Return the number of data blocks.
    pub fn n_data_blocks(&self) -> Result<usize> {
        Ok(self.get_index_block()?.data_block_start_keys.len())
    }

    /// Get a logical partition for future access.
    /// Note: This function will download the data block if it is not in IA.
    pub async fn get_logical_partition(&self, lp_key: &[u8]) -> Result<Option<PackedFileLp>> {
        let lp_filter = self.get_lp_filter()?;
        let key_hash = farmhash::fingerprint64(lp_key);
        if !lp_filter.contains(&key_hash) {
            return Ok(None);
        }

        // Navigate the data block idx from index block.
        let index_block = self.get_index_block()?;
        let data_block_idx = self.find_data_block_index(&index_block, lp_key);
        let data_block_idx = match data_block_idx {
            Some(idx) => idx,
            None => return Ok(None),
        };

        // Retrieve the data of data block.
        let data_block_offset = index_block.data_block_offsets[data_block_idx];
        let data_block_len = index_block.data_block_offsets[data_block_idx + 1] - data_block_offset;

        // TODO: Avoid repeat mmap when segment is already mapped.
        let (data, guard) = self
            .0
            .file
            .mmap_range(data_block_offset as u64, data_block_len as usize)
            .await?;

        let data_block = PackedFileDataBlockAccessor::new(data, guard)?;

        let need_checksum = {
            let data_block_checked = self.0.data_block_checked.read().unwrap();
            data_block_checked.is_empty() || !data_block_checked[data_block_idx]
        };
        if need_checksum {
            let checksum_type = ChecksumType::from(self.0.footer.checksum_type as u8);
            data_block.verify_checksum(checksum_type)?;
            {
                let mut data_block_checked = self.0.data_block_checked.write().unwrap();
                if data_block_checked.is_empty() {
                    data_block_checked.resize(index_block.data_block_offsets.len(), false);
                }
                data_block_checked[data_block_idx] = true;
            }
        }

        data_block.find_lp(lp_key)
    }

    /// Find the data block index that may contain the given logical partition
    /// key. It returns None when data block must not exist (there is no
    /// data block, or the key is smaller than the key of the first data
    /// block).
    ///
    /// If a data block is returned, you should further check whether the data
    /// block contains the given logical partition key by yourself.
    fn find_data_block_index(
        &self,
        index_block: &ftspb::PackedFileIndexBlock,
        lp_key: &[u8],
    ) -> Option<usize> {
        let n = index_block.data_block_start_keys.len();
        let pos = crate::table::search(n, |i| {
            index_block.data_block_start_keys[i].as_slice() > lp_key
        });
        // Now: [..pos), start_key <= lp_key
        //      [pos..): start_key > lp_key
        // So pos-1 is what we want.
        if pos == 0 {
            return None;
        }
        Some(pos - 1)
    }
}

/// A zero-copy accessor for reading a data block in PackedFile.
/// Creating the accessor is designed to be very cheap.
///
/// DataBlock layout:
/// ...    (Align to 8 bytes)
/// ...    Entries
///        > Each entry is a logical partition
///        > Each entry is aligned to 8 bytes
///        > Entries are ordered by LPKey
/// ...    (Align to 8 bytes)
/// [u32]  Entry Offsets (n=Entries Count)
/// u32    (Additional) Last Entry End Offset
/// u32    Entries Count
/// u32    Checksum (start from 0 byte including paddings)
struct PackedFileDataBlockAccessor {
    data_block: Bytes,
    source_guard: IaMmapSource,

    /// It contains n_entries+1 elements, where the last element is the end
    /// offset.
    ///
    /// 'static because comes from `entries_data`.
    /// MUST NOT expose to outside.
    offsets: &'static [u32],
    n_entries: u32,
    checksum: u32,
}

impl PackedFileDataBlockAccessor {
    fn new(mut data_block: Bytes, source_guard: IaMmapSource) -> Result<Self> {
        let data_block_clone = data_block.clone();
        let checksum = data_block.try_get_last_u32_le()?;
        let n_entries = data_block.try_get_last_u32_le()?;
        let offsets_data = data_block.try_get_last((n_entries + 1) as usize * 4)?;
        let offsets = offsets_data.try_as_slice::<u32>()?;
        Ok(PackedFileDataBlockAccessor {
            data_block: data_block_clone,
            source_guard,

            offsets: unsafe { extend_lifetime(offsets) },
            n_entries,
            checksum,
        })
    }

    fn verify_checksum(&self, checksum_type: ChecksumType) -> Result<()> {
        let content = &self.data_block.as_ref()[..self.data_block.len() - 4];
        let checksum_actual = checksum_type.checksum(content);
        if self.checksum != checksum_actual {
            bail!(
                "FtsPackedFile data block checksum mismatch, actual {:#08x}, expect {:#08x}, ChecksumType={:?}",
                checksum_actual,
                self.checksum,
                checksum_type
            );
        }
        Ok(())
    }

    fn entry_data_at(&self, i: usize) -> Result<Bytes> {
        let offset = self.offsets[i] as usize;
        let end_offset = self.offsets[i + 1] as usize;
        let entry_data = self.data_block.try_slice(offset..end_offset)?;
        entry_data.check_aligned::<u64>()?;
        Ok(entry_data)
    }

    /// Extracts the LPKey from the entry data without constructing
    /// PackedFileLp.
    fn fast_extract_lp_key(entry_data: &Bytes) -> Result<Bytes> {
        let mut entry_data = entry_data.clone();
        let key_len = entry_data.try_get_u16_le()?;
        if key_len == 0 {
            bail!("Broken entry data, unexpected empty key length");
        }
        entry_data.try_get_first(key_len as usize)
    }

    fn find_lp(&self, lp_key: &[u8]) -> Result<Option<PackedFileLp>> {
        let n_entries = self.n_entries as usize;
        let pos = crate::table::try_search(n_entries, |i| {
            let entry_data = self.entry_data_at(i)?;
            Self::fast_extract_lp_key(&entry_data).map(|key| key.as_ref() >= lp_key)
        })?;
        if pos == n_entries {
            return Ok(None);
        }
        let entry_data = self.entry_data_at(pos)?;
        let lp_key_at_pos = Self::fast_extract_lp_key(&entry_data)?;
        if lp_key_at_pos.as_ref() != lp_key {
            return Ok(None);
        }
        Ok(Some(PackedFileLp::new(
            entry_data,
            self.source_guard.clone(),
        )?))
    }
}

/// Reading from a logical partition in PackedFile.
/// This struct is supposed to be cached.
///
/// Entry data layout:
/// u16    LogicalPartitionKey Length
/// ...    LogicalPartitionKey Bytes
/// u32    Number of Primary Keys (n_pk)
///        > Primary keys may duplicate, means there are multiple versions.
/// u8     Flag - Primary Key is Int Handle (is_pk_int)
/// ...    (Align to 8 bytes)
/// [u64]  Versions (For the same PK, they are ordered from largest to smallest)
/// [u8]   Delete Marks
/// ...    (Align to 4 bytes)
/// [u32]  Common Handle Offsets (only for Common Handle), n=n_pk+1
/// ...    (Align to 8 bytes)
/// ...    Primary Key Data Bytes (ordered by PK in ascending order)
/// u32    Primary Key Filter Length
/// ...    Primary Key Filter Bytes
/// u32    Tantivy Index Data Length
/// ...    Tantivy Index Data Bytes
/// ...    (Align to 8 bytes)
///
/// For example, we may have such data (unordered):
/// (PK, Version)
/// (7,  2)
/// (1,  1)
/// (1,  3)
/// (3,  100)
///
/// They will be stored in PackedFileLp in this way:
/// PK:      [1, 1, 3,   7] (ascending order)
/// Version: [3, 1, 100, 2] (newer version comes first for the same PK)
#[derive(Clone)]
pub struct PackedFileLp(Arc<PackedFileLpCore>);

struct PackedFileLpCore {
    _entry_data: Bytes, /* Contains the whole entry data. It must be kept so that the rest
                         * fields' lifetime is valid */

    #[allow(unused)]
    source_guard: IaMmapSource,

    lp_key: Bytes,

    n_pk: u32,
    is_pk_int: bool,

    /// 'static because comes from `entry_data`.
    /// MUST NOT expose to outside.
    versions: &'static [u64],
    /// 'static because comes from `entry_data`.
    /// MUST NOT expose to outside.
    #[allow(unused)]
    delete_marks: &'static [u8],

    /// Only exists when pk is common PK (is_pk_int == false).
    /// It contains n_pk+1 elements, where the last element is the end offset.
    /// 'static because comes from `entry_data`.
    /// MUST NOT expose to outside.
    pk_common_offsets: &'static [u32],

    // for Int PK, size=n_pk*8, must be aligned by 8 bytes
    // for Common PK, size varies.
    pk_data: Bytes,

    pk_filter: BinaryFuse8, // The underlying storage is still entry_data, sharing same lifecycle.

    #[allow(unused)]
    tantivy_index_data: Bytes,
    // tantivy_index_cache: TtlCache<clara_fts::IndexReader>,
}

impl PackedFileLp {
    /// Creates a new PackedFileEntryDataAccessor from the given entry data.
    /// PackedFileEntryDataAccessor is too large so it is wrapped in a Box.
    fn new(mut entry_data: Bytes, source_guard: IaMmapSource) -> Result<PackedFileLp> {
        entry_data.check_aligned::<u64>()?;

        let mut current_offset = 0usize;

        // Read LogicalPartitionKey Length and skip key bytes
        let lp_key_len = entry_data.try_get_u16_le()? as usize;
        current_offset += 2;
        let lp_key = entry_data.try_get_first(lp_key_len)?;
        current_offset += lp_key_len;

        // Read Number of Primary Keys
        let n_pk = entry_data.try_get_u32_le()?;
        current_offset += 4;

        // Read Flag - Primary Key is Int Handle
        let is_pk_int = entry_data.try_get_u8()? != 0;
        current_offset += 1;

        // Align to 8 bytes
        let next_offset = next_aligned_offset(current_offset, 8);
        if next_offset > current_offset {
            entry_data.advance(next_offset - current_offset);
            current_offset = next_offset;
        }

        // Read Versions (n_pk * 8 bytes)
        let versions_data = entry_data.try_get_first((n_pk as usize) * 8)?;
        let versions = versions_data.try_as_slice::<u64>()?;
        current_offset += versions_data.len();

        // Read Delete Marks (n_pk bytes)
        let delete_marks_data = entry_data.try_get_first(n_pk as usize)?;
        let delete_marks = delete_marks_data.try_as_slice::<u8>()?;
        current_offset += delete_marks_data.len();

        // Align to 4 bytes
        let next_offset = next_aligned_offset(current_offset, 4);
        if next_offset > current_offset {
            entry_data.advance(next_offset - current_offset);
            current_offset = next_offset;
        }

        // Read Common Handle Offsets (only for Common Handle)
        let offsets_data = if !is_pk_int {
            entry_data.try_get_first(((n_pk + 1) as usize) * 4)?
        } else {
            Bytes::new() // No offsets for Int handle
        };
        current_offset += offsets_data.len();
        let pk_common_offsets = offsets_data.try_as_slice::<u32>()?;

        // Align to 8 bytes for Primary Key Data
        let next_offset = next_aligned_offset(current_offset, 8);
        if next_offset > current_offset {
            entry_data.advance(next_offset - current_offset);
            current_offset = next_offset;
        }

        // Read Primary Key Data
        let pk_data_size = if is_pk_int {
            // For Int handle: n_pk * 8 bytes
            (n_pk as usize) * 8
        } else {
            // For Common handle
            pk_common_offsets.last().copied().unwrap() as usize
        };

        let pk_data = entry_data.try_get_first(pk_data_size)?;
        pk_data.check_aligned::<u64>()?;
        current_offset += pk_data_size;

        // Read Primary Key Filter Length and Filter Bytes
        let pk_filter_len = entry_data.try_get_u32_le()? as usize;
        let pk_filter_data = entry_data.try_get_first(pk_filter_len)?;
        current_offset += pk_filter_len;

        let pk_filter = BinaryFuse8::try_from_bytes(&pk_filter_data)
            .map_err(|e| anyhow!("Bad PK filter: {}", e))?;

        // Read Tantivy Index Data Length and Index Data Bytes
        let tantivy_len = entry_data.try_get_u32_le()? as usize;
        current_offset += 4;
        let tantivy_index_data = entry_data.try_get_first(tantivy_len)?;

        // Make lint happy
        _ = current_offset;

        Ok(PackedFileLp(Arc::new(PackedFileLpCore {
            _entry_data: entry_data,
            source_guard,
            lp_key,
            n_pk,
            is_pk_int,
            versions: unsafe { extend_lifetime(versions) },
            delete_marks: unsafe { extend_lifetime(delete_marks) },
            pk_common_offsets: unsafe { extend_lifetime(pk_common_offsets) },
            pk_data,
            pk_filter,
            tantivy_index_data,
        })))
    }

    /// Returns the Logical Partition Key of this LogicalPartition.
    pub fn lp_key(&self) -> &[u8] {
        &self.0.lp_key
    }

    /// Returns the number of primary keys (includes multi-version).
    pub fn n_keys(&self) -> usize {
        self.0.n_pk as usize
    }

    /// Returns whether the primary key is int.
    pub fn is_pk_int(&self) -> bool {
        self.0.is_pk_int
    }

    /// Returns the approximate memory size of this LP.
    /// Usually the approximate size is used for cache weight.
    pub fn memory_size(&self) -> usize {
        // Most data is mmapped so that they do not count towards the weight.
        std::mem::size_of::<PackedFileLpCore>() + self.0.pk_filter.len()
    }

    /// Check if this file stores the specified PK with its version > given
    /// version and its version <= max_version. This is used for MVCC check.
    pub fn has_newer_version(&self, pk: &[u8], version: u64, max_version: u64) -> Result<bool> {
        if self.0.is_pk_int && pk.len() != 8 {
            bail!(
                "Invalid int PK length: expected 8 bytes, got {}, PK={}",
                pk.len(),
                hex(pk)
            );
        }

        let pk_hash = farmhash::fingerprint64(pk);
        if !self.0.pk_filter.contains(&pk_hash) {
            return Ok(false);
        }

        if self.0.is_pk_int {
            let pk_int = LittleEndian::read_u64(pk);
            let pks = self.0.pk_data.try_as_slice::<u64>()?;
            has_newer_version(
                pk_int,
                version,
                max_version,
                self.0.n_pk as usize,
                |i| Ok(self.0.versions[i]),
                |i| Ok(pks[i]),
            )
        } else {
            has_newer_version(
                pk,
                version,
                max_version,
                self.0.n_pk as usize,
                |i| Ok(self.0.versions[i]),
                |i| {
                    let start_offset = self.0.pk_common_offsets[i] as usize;
                    let end_offset = self.0.pk_common_offsets[i + 1] as usize;
                    self.0
                        .pk_data
                        .as_ref()
                        .get(start_offset..end_offset)
                        .ok_or_else(|| {
                            anyhow!(
                                "PK data out of bounds: start={}, end={}, pk_data_len={}",
                                start_offset,
                                end_offset,
                                self.0.pk_data.len()
                            )
                        })
                },
            )
        }
    }
}

/// Find whether there is a newer version of a specified PK.
///
/// We suppose PKs and versions are ordered in the following way and accessed
/// through a xxx_at() fn:
///
/// N:       4
/// PK:      [1, 1, 3,   7] (ascending order)
/// Version: [3, 1, 100, 2] (newer version comes first for the same PK)
///
/// Internally, we treat it as a globally sorted sequence of (PK, !version)
/// tuples: (1, !3), (1, !1), (3, !100), (7, !2)
/// and then we perform a binary search to find the first position
/// where (PK, !version) >= (target_pk, !target_max_version).
///
/// Returns true, if the `target_pk` is found and it has a version that is newer
/// than `target_version` and not newer than `target_max_version`.
fn has_newer_version<PK: Ord>(
    target_pk: PK,
    target_version: u64,
    target_max_version: u64,

    n: usize,
    version_at: impl Fn(usize) -> Result<u64>,
    pk_at: impl Fn(usize) -> Result<PK>,
) -> Result<bool> {
    use std::cmp::Ordering;
    // Find the first position `pos` such that:
    // `pk_at(pos) >= target_pk` AND `version_at(pos) <= target_max_version`.
    let pos = crate::table::try_search::<anyhow::Error>(n, |i| match pk_at(i)?.cmp(&target_pk) {
        Ordering::Less => Ok(false),
        Ordering::Greater => Ok(true),
        Ordering::Equal => Ok(version_at(i)? <= target_max_version),
    })?;
    if pos >= n {
        return Ok(false);
    }

    let pk = pk_at(pos)?;
    if pk == target_pk {
        let version = version_at(pos)?;
        // We already know version <= target_max_version from the search.
        // We just need to check if version > target_version.
        if version > target_version {
            return Ok(true);
        }
    }

    Ok(false)
}

unsafe fn extend_lifetime<'b, T: ?Sized>(r: &'b T) -> &'static T {
    std::mem::transmute::<&'b T, &'static T>(r)
}

#[cfg(test)]
mod tests {
    use std::io::Cursor;

    use super::*;
    use crate::{dfs::FileType, ia::manager::tests::IaFileTester};

    #[test]
    fn footer_marshal_unmarshal_with_checksum() {
        let mut f = PackedFileFooter::new();
        f.checksum_type = ChecksumType::Crc32c;
        f.checksum_other_meta = 0xA1B2_C3D4;
        f.index_block_offset = 0x10_20_30_40;
        f.lp_filter_block_offset = 0x20_30_40_50;
        f.prop_offset = 0x30_40_50_60;

        let mut buf = Vec::new();
        f.marshal(&mut buf).unwrap();

        // Roundtrip
        let f2 = PackedFileFooter::unmarshal(&buf).unwrap();
        assert_eq!(f2.format, f.format);
        assert_eq!(f2.checksum_type.value(), f.checksum_type.value());
        assert_eq!(f2.checksum_other_meta, f.checksum_other_meta);
        assert_eq!(f2.index_block_offset, f.index_block_offset);
        assert_eq!(f2.lp_filter_block_offset, f.lp_filter_block_offset);
        assert_eq!(f2.prop_offset, f.prop_offset);
        assert_eq!(f2.magic, f.magic);

        // Modify some byte should cause checksum mismatch
        let mut buf2 = buf.clone();
        buf2[8] ^= 0xFF; // Flip a bit in checksum field
        PackedFileFooter::unmarshal(&buf2).unwrap_err();

        // Modify some data field should also cause checksum mismatch
        let mut buf2 = buf.clone();
        buf2[15] ^= 0xFF;
        PackedFileFooter::unmarshal(&buf2).unwrap_err();
    }

    #[test]
    fn footer_unmarshal_invalid_size() {
        // Too short
        PackedFileFooter::unmarshal(&[]).unwrap_err();
        // Too long
        let mut v = vec![0u8; FTS_PACKED_FILE_FOOTER_SIZE + 1];
        PackedFileFooter::unmarshal(&v).unwrap_err();
        // Off by one short
        v.truncate(FTS_PACKED_FILE_FOOTER_SIZE - 1);
        PackedFileFooter::unmarshal(&v).unwrap_err();
    }

    #[test]
    fn footer_unmarshal_invalid_magic() {
        let mut f = PackedFileFooter::new();
        f.checksum_type = ChecksumType::Crc32c; // any type
        let mut buf = Vec::new();
        f.marshal(&mut buf).unwrap();
        // Corrupt magic
        buf[28..32].copy_from_slice(&0xDEAD_BEEFu32.to_le_bytes());
        PackedFileFooter::unmarshal(&buf).unwrap_err();
    }

    #[test]
    fn test_has_newer_version() {
        // Test data: PK: [1, 1, 3, 7], Version: [3, 1, 100, 2]
        // This represents: (1,3), (1,1), (3,100), (7,2)
        let pk_data = [1u64, 1, 3, 7];
        let versions = [3u64, 1, 100, 2];

        let version_at = |i: usize| -> Result<u64> { Ok(versions[i]) };
        let pk_at = |i: usize| -> Result<u64> { Ok(pk_data[i]) };

        // PK=1, version=0, max_version=5 -> should find (1,v3) and (1,v1)
        assert!(has_newer_version(1u64, 0, 5, pk_data.len(), version_at, pk_at).unwrap());

        // PK=1, version=1, max_version=5 -> should find (1,v3)
        assert!(has_newer_version(1u64, 1, 5, pk_data.len(), version_at, pk_at).unwrap());

        // PK=1, version=2, max_version=5 -> should find (1,v3)
        assert!(has_newer_version(1u64, 2, 5, pk_data.len(), version_at, pk_at).unwrap());

        // PK=1, version=3, max_version=5 -> no version > 3
        assert!(!has_newer_version(1u64, 3, 5, pk_data.len(), version_at, pk_at).unwrap());

        // PK=1, version=10, max_version=150 -> no version > 10
        assert!(!has_newer_version(1u64, 10, 150, pk_data.len(), version_at, pk_at).unwrap());

        // PK=1, version=0, max_version=2 -> should find (1,v3)
        assert!(has_newer_version(1u64, 0, 2, pk_data.len(), version_at, pk_at).unwrap());

        // PK=2, not exists
        assert!(!has_newer_version(2u64, 0, 1000, pk_data.len(), version_at, pk_at).unwrap());

        // PK=3, version=50, max_version=150 -> should find (3,100)
        assert!(has_newer_version(3u64, 50, 150, pk_data.len(), version_at, pk_at).unwrap());

        // PK=3, version=50, max_version=100 -> should find (3,100)
        assert!(has_newer_version(3u64, 50, 100, pk_data.len(), version_at, pk_at).unwrap());

        // PK=3, version=1, max_version=50 -> (3,100) is newer than
        // 1 but exceeds max_version
        assert!(!has_newer_version(3u64, 1, 50, pk_data.len(), version_at, pk_at).unwrap());

        // PK=7, version=1, max_version=3 -> should find (7,2)
        assert!(has_newer_version(7u64, 1, 3, pk_data.len(), version_at, pk_at).unwrap());

        // PK=999, not exists
        assert!(!has_newer_version(999u64, 0, 1000, pk_data.len(), version_at, pk_at).unwrap());

        // Edge case: empty data
        assert!(!has_newer_version(1u64, 0, 1000, 0, version_at, pk_at).unwrap());

        // Edge case: exact version match with max_version
        assert!(!has_newer_version(1u64, 3, 3, pk_data.len(), version_at, pk_at).unwrap());
        assert!(has_newer_version(1u64, 2, 3, pk_data.len(), version_at, pk_at).unwrap());
    }

    fn get_packed_file_metadata_offset(file_content: &[u8]) -> u64 {
        assert!(file_content.len() >= FTS_PACKED_FILE_FOOTER_SIZE);
        let footer = PackedFileFooter::unmarshal(
            &file_content[file_content.len() - FTS_PACKED_FILE_FOOTER_SIZE..],
        )
        .unwrap();
        footer.metadata_offset()
    }

    #[test]
    fn test_int_pk() -> Result<()> {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        // Create test data with int PKs
        // First LP: lp1
        builder.start_lp(true, b"lp1")?;
        builder.add_pk_int(1, 100, false)?;
        builder.add_pk_int(2, 99, false)?;
        builder.add_pk_int(3, 98, true)?;
        builder.set_tantivy_index(b"tantivy_data_1")?;
        builder.finish_lp()?;

        // Second LP: lp2
        builder.start_lp(true, b"lp2")?;
        // Include an overlapping PK (1) that also exists in lp1 to ensure
        // LPs are independent even if PK bytes are the same.
        builder.add_pk_int(1, 60, false)?;
        builder.add_pk_int(10, 50, false)?;
        builder.add_pk_int(20, 49, false)?;
        builder.set_tantivy_index(b"tantivy_data_2")?;
        builder.finish_lp()?;

        builder.finish()?;

        let tester = IaFileTester::default();
        let ia_file = tester.new_ia_file(
            FileType::FtsPackedFile,
            &buffer,
            get_packed_file_metadata_offset(&buffer),
        );
        let packed_file = PackedFile::new(Arc::new(ia_file))?;
        tester.run_async(async move {
            let pk1 = 1u64.to_le_bytes();
            let pk3 = 3u64.to_le_bytes();

            let lp1 = packed_file.get_logical_partition(b"lp1").await?.unwrap();
            assert_eq!(lp1.lp_key(), b"lp1");
            assert_eq!(lp1.n_keys(), 3);
            assert!(lp1.has_newer_version(&pk1, 50, 150)?);
            assert!(!lp1.has_newer_version(&pk1, 100, 150)?);
            assert!(lp1.has_newer_version(&pk3, 10, 150)?);
            assert!(!lp1.has_newer_version(&pk3, 98, 150)?);

            let lp2 = packed_file.get_logical_partition(b"lp2").await?.unwrap();
            assert_eq!(lp2.lp_key(), b"lp2");
            assert_eq!(lp2.n_keys(), 3);

            let pk10 = 10u64.to_le_bytes();
            // Overlapping PK across LPs: pk1 exists in both lp1 (v=100) and lp2 (v=60)
            assert!(lp2.has_newer_version(&pk1, 50, 150)?);
            assert!(!lp2.has_newer_version(&pk1, 60, 150)?);
            assert!(lp2.has_newer_version(&pk10, 10, 150)?);
            assert!(!lp2.has_newer_version(&pk10, 50, 150)?);

            let missing = packed_file.get_logical_partition(b"lp_missing").await?;
            assert!(missing.is_none());
            assert_eq!(packed_file.n_data_blocks()?, 1);
            Ok(())
        })
    }

    #[test]
    fn test_common_pk() -> Result<()> {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        // Create test data with common PKs
        builder.start_lp(false, b"lp_common1")?;
        builder.add_pk_common(b"pk_a", 200, false)?;
        builder.add_pk_common(b"pk_b", 199, false)?;
        builder.add_pk_common(b"pk_c", 198, true)?;
        builder.set_tantivy_index(b"tantivy_common_1")?;
        builder.finish_lp()?;

        builder.finish()?;

        let tester = IaFileTester::default();
        let ia_file = tester.new_ia_file(
            FileType::FtsPackedFile,
            &buffer,
            get_packed_file_metadata_offset(&buffer),
        );
        let packed_file = PackedFile::new(Arc::new(ia_file))?;
        tester.run_async(async move {
            let lp = packed_file
                .get_logical_partition(b"lp_common1")
                .await?
                .unwrap();
            assert_eq!(lp.n_keys(), 3);
            assert!(lp.has_newer_version(b"pk_a", 150, 250)?);
            assert!(!lp.has_newer_version(b"pk_a", 200, 250)?);
            assert!(lp.has_newer_version(b"pk_b", 150, 250)?);
            assert!(lp.has_newer_version(b"pk_c", 150, 300)?);
            assert!(!lp.has_newer_version(b"pk_c", 198, 300)?);
            assert_eq!(packed_file.n_data_blocks()?, 1);
            Ok(())
        })
    }

    #[test]
    fn test_mixed_int_and_common_pk() -> Result<()> {
        // Build a packed file containing exactly two LPs: one int-PK LP and one
        // common-PK LP.
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions {
                block_size: 1024,
                checksum_type: ChecksumType::Crc32c,
            },
        );

        // Int PK LP: lp_int (will be added after lp_comm to respect ordering)
        // Common PK LP: lp_comm comes first lexicographically ("lp_comm" < "lp_int")
        builder.start_lp(false, b"lp_comm")?;
        builder.add_pk_common(b"a_key", 500, false)?;
        builder.add_pk_common(b"a_key", 450, false)?; // older same key
        builder.add_pk_common(b"b_key", 400, true)?;
        builder.add_pk_common(b"c_key", 350, false)?;
        builder.set_tantivy_index(b"idx_comm")?;
        builder.finish_lp()?;

        builder.start_lp(true, b"lp_int")?;
        builder.add_pk_int(10, 300, false)?; // Newest
        builder.add_pk_int(10, 250, false)?; // Older version same PK
        builder.add_pk_int(11, 200, true)?; // A delete mark PK
        builder.set_tantivy_index(b"idx_int")?;
        builder.finish_lp()?;

        builder.finish()?;

        let tester = IaFileTester::default();
        let ia_file = tester.new_ia_file(
            FileType::FtsPackedFile,
            &buffer,
            get_packed_file_metadata_offset(&buffer),
        );
        let packed_file = PackedFile::new(Arc::new(ia_file))?;
        tester.run_async(async move {
            let lp_int = packed_file.get_logical_partition(b"lp_int").await?.unwrap();
            let lp_comm = packed_file
                .get_logical_partition(b"lp_comm")
                .await?
                .unwrap();
            assert_eq!(lp_int.lp_key(), b"lp_int");
            assert_eq!(lp_comm.lp_key(), b"lp_comm");
            assert_eq!(lp_int.n_keys(), 3); // 10x2 + 11
            assert_eq!(lp_comm.n_keys(), 4); // a_key x2 + b_key + c_key

            // Int PK checks
            let pk10 = 10u64.to_le_bytes();
            let pk11 = 11u64.to_le_bytes();
            // pk10 has versions 300 & 250
            assert!(lp_int.has_newer_version(&pk10, 200, 400)?); // 300,250 >200
            assert!(lp_int.has_newer_version(&pk10, 250, 400)?); // 300 >250
            assert!(!lp_int.has_newer_version(&pk10, 300, 400)?); // none >300
            assert!(lp_int.has_newer_version(&pk10, 200, 299)?); // 250 fits <=299 >200
            assert!(!lp_int.has_newer_version(&pk10, 200, 249)?); // neither 300 nor 250 <=249
            // pk11 only one version 200
            assert!(lp_int.has_newer_version(&pk11, 150, 400)?);
            assert!(!lp_int.has_newer_version(&pk11, 200, 400)?);

            // Common PK checks
            assert!(lp_comm.has_newer_version(b"a_key", 100, 600)?); // 500,450
            assert!(lp_comm.has_newer_version(b"a_key", 450, 600)?); // 500
            assert!(!lp_comm.has_newer_version(b"a_key", 500, 600)?);
            assert!(lp_comm.has_newer_version(b"b_key", 300, 600)?); // 400
            assert!(!lp_comm.has_newer_version(b"b_key", 400, 600)?);
            assert!(lp_comm.has_newer_version(b"c_key", 300, 600)?); // 350
            assert!(!lp_comm.has_newer_version(b"c_key", 350, 600)?);

            // Cross-LP negative: keys should not appear in other LP
            let none = packed_file.get_logical_partition(b"nonexistent_lp").await?;
            assert!(none.is_none());
            assert_eq!(packed_file.n_data_blocks()?, 1);
            Ok(())
        })
    }

    #[test]
    fn test_builder_multiple_data_blocks() -> Result<()> {
        // Force extremely small block size so each LP will reside in its own data
        // block.
        let options = PackedFileBuilderOptions {
            block_size: 1,
            checksum_type: ChecksumType::Crc32c,
        };

        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(Cursor::new(&mut buffer), options);

        // LP keys must be in ascending order: lp_a < lp_b < lp_c
        // LP 1: lp_a (int PKs)
        builder.start_lp(true, b"lp_a")?;
        builder.add_pk_int(1, 300, false)?; // Single version
        builder.add_pk_int(2, 250, true)?; // Delete mark
        builder.set_tantivy_index(b"tantivy_lp_a")?;
        builder.finish_lp()?;

        // LP 2: lp_b
        builder.start_lp(true, b"lp_b")?;
        builder.add_pk_int(10, 200, false)?;
        builder.add_pk_int(20, 190, false)?;
        builder.add_pk_int(30, 180, true)?;
        builder.set_tantivy_index(b"tantivy_lp_b")?;
        builder.finish_lp()?;

        // LP 3: lp_c with duplicate PK versions
        builder.start_lp(true, b"lp_c")?;
        builder.add_pk_int(100, 160, false)?; // Newest
        builder.add_pk_int(100, 150, false)?; // Older version same PK
        builder.add_pk_int(110, 140, true)?;
        builder.set_tantivy_index(b"tantivy_lp_c")?;
        builder.finish_lp()?;

        builder.finish()?;

        let tester = IaFileTester::default();
        let ia_file = tester.new_ia_file(
            FileType::FtsPackedFile,
            &buffer,
            get_packed_file_metadata_offset(&buffer),
        );
        let packed_file = PackedFile::new(Arc::new(ia_file))?;
        tester.run_async(async move {
            // lp_a validations
            let lp_a = packed_file.get_logical_partition(b"lp_a").await?.unwrap();
            let pk1 = 1u64.to_le_bytes();
            let pk2 = 2u64.to_le_bytes();
            assert_eq!(lp_a.n_keys(), 2);
            assert!(lp_a.has_newer_version(&pk1, 100, 400)?);
            assert!(!lp_a.has_newer_version(&pk1, 300, 400)?);
            assert!(lp_a.has_newer_version(&pk2, 0, 400)?); // delete-mark version still counts
            assert!(!lp_a.has_newer_version(&pk2, 250, 400)?);

            // lp_b validations
            let lp_b = packed_file.get_logical_partition(b"lp_b").await?.unwrap();
            let pk10 = 10u64.to_le_bytes();
            let pk30 = 30u64.to_le_bytes();
            assert_eq!(lp_b.n_keys(), 3);
            assert!(lp_b.has_newer_version(&pk10, 150, 400)?);
            assert!(!lp_b.has_newer_version(&pk10, 200, 400)?);
            assert!(lp_b.has_newer_version(&pk30, 100, 400)?);
            assert!(!lp_b.has_newer_version(&pk30, 180, 400)?);

            // lp_c validations
            let lp_c = packed_file.get_logical_partition(b"lp_c").await?.unwrap();
            let pk100 = 100u64.to_le_bytes();
            let pk110 = 110u64.to_le_bytes();
            assert_eq!(lp_c.n_keys(), 3);
            assert!(lp_c.has_newer_version(&pk100, 120, 400)?); // 160,150 >120
            assert!(lp_c.has_newer_version(&pk100, 150, 400)?); // 160 >150
            assert!(!lp_c.has_newer_version(&pk100, 160, 400)?); // none >160
            assert!(lp_c.has_newer_version(&pk110, 100, 400)?); // 140
            assert!(!lp_c.has_newer_version(&pk110, 140, 400)?);

            // Negative cases
            let none = packed_file.get_logical_partition(b"lp_d").await?;
            assert!(none.is_none());
            assert_eq!(packed_file.n_data_blocks()?, 3);
            Ok(())
        })
    }

    #[test]
    fn test_empty_file() -> Result<()> {
        let options = PackedFileBuilderOptions::default();

        let mut buffer = Vec::new();
        let builder = PackedFileBuilder::new(Cursor::new(&mut buffer), options);
        builder.finish()?;

        let tester = IaFileTester::default();
        let ia_file = tester.new_ia_file(
            FileType::FtsPackedFile,
            &buffer,
            get_packed_file_metadata_offset(&buffer),
        );
        let packed_file = PackedFile::new(Arc::new(ia_file))?;
        tester.run_async(async move {
            assert!(
                packed_file
                    .get_logical_partition(b"anything")
                    .await?
                    .is_none()
            );
            assert_eq!(packed_file.n_data_blocks()?, 0);
            Ok(())
        })
    }

    #[test]
    fn test_roundtrip_multiple_lps_with_tantivy_data() -> Result<()> {
        let options = PackedFileBuilderOptions {
            block_size: 512, // Smaller block size to test multiple blocks
            checksum_type: ChecksumType::Crc32c,
        };

        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(Cursor::new(&mut buffer), options);

        // Create multiple LPs with different tantivy data
        builder.start_lp(true, b"lp1")?;
        builder.add_pk_int(1, 100, false)?;
        builder.add_pk_int(2, 99, false)?;
        builder.set_tantivy_index(b"tantivy_data_for_lp1")?;
        builder.finish_lp()?;

        builder.start_lp(false, b"lp2")?;
        builder.add_pk_common(b"key_a", 200, false)?;
        builder.add_pk_common(b"key_b", 199, true)?;
        builder.set_tantivy_index(b"tantivy_data_for_lp2")?;
        builder.finish_lp()?;

        builder.start_lp(true, b"lp3")?;
        builder.add_pk_int(10, 300, false)?;
        builder.add_pk_int(20, 299, false)?;
        builder.add_pk_int(30, 298, true)?;
        builder.set_tantivy_index(b"tantivy_data_for_lp3")?;
        builder.finish_lp()?;

        builder.finish()?;

        // Create IaFile using IaFileTester
        let tester = IaFileTester::default();
        let ia_file = tester.new_ia_file(
            FileType::FtsPackedFile,
            &buffer,
            get_packed_file_metadata_offset(&buffer),
        );
        let packed_file = PackedFile::new(Arc::new(ia_file))?;
        tester.run_async(async move {
            let lp1 = packed_file.get_logical_partition(b"lp1").await?.unwrap();
            assert_eq!(lp1.lp_key(), b"lp1");
            assert_eq!(lp1.n_keys(), 2);
            let lp2 = packed_file.get_logical_partition(b"lp2").await?.unwrap();
            assert_eq!(lp2.lp_key(), b"lp2");
            assert_eq!(lp2.n_keys(), 2);
            let lp3 = packed_file.get_logical_partition(b"lp3").await?.unwrap();
            assert_eq!(lp3.lp_key(), b"lp3");
            assert_eq!(lp3.n_keys(), 3);
            let pk1 = 1u64.to_le_bytes();
            assert!(lp1.has_newer_version(&pk1, 50, 150)?);
            assert!(!lp1.has_newer_version(&pk1, 100, 150)?);
            assert!(lp2.has_newer_version(b"key_a", 150, 250)?);
            assert!(!lp2.has_newer_version(b"key_a", 200, 250)?);
            let pk30 = 30u64.to_le_bytes();
            assert!(lp3.has_newer_version(&pk30, 250, 350)?);
            assert!(!lp3.has_newer_version(&pk30, 298, 350)?);
            let lp_none = packed_file.get_logical_partition(b"lp_nonexistent").await?;
            let n_blocks = packed_file.n_data_blocks()?;
            assert!(n_blocks >= 1);
            assert!(lp_none.is_none());
            Ok(())
        })
    }

    #[test]
    fn test_checksum_data_block_corruption() -> Result<()> {
        // Build a simple file with checksum enabled.
        let options = PackedFileBuilderOptions {
            block_size: 1024,
            checksum_type: ChecksumType::Crc32c,
        };
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(Cursor::new(&mut buffer), options);

        builder.start_lp(true, b"lp1")?;
        builder.add_pk_int(1, 100, false)?;
        builder.finish_lp()?;
        builder.finish()?;

        // Corrupt a byte in the data block
        buffer[5] ^= 0xFF;

        // Open and read should fail with data block checksum mismatch.
        let tester = IaFileTester::default();
        let ia_file = tester.new_ia_file(
            FileType::FtsPackedFile,
            &buffer,
            get_packed_file_metadata_offset(&buffer),
        );
        let packed_file = PackedFile::new(Arc::new(ia_file))?;
        tester.run_async(async move {
            let err = packed_file
                .get_logical_partition(b"lp1")
                .await
                .err()
                .unwrap()
                .to_string();
            assert!(err.contains("data block checksum mismatch"));
        });

        Ok(())
    }

    #[test]
    fn test_get_lp_edge_cases() -> Result<()> {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        builder.start_lp(true, b"lp_b")?;
        builder.add_pk_int(1, 100, false)?;
        builder.finish_lp()?;

        builder.start_lp(true, b"lp_d")?;
        builder.add_pk_int(2, 200, false)?;
        builder.finish_lp()?;

        builder.finish()?;

        let tester = IaFileTester::default();
        let ia_file = tester.new_ia_file(
            FileType::FtsPackedFile,
            &buffer,
            get_packed_file_metadata_offset(&buffer),
        );
        let packed_file = PackedFile::new(Arc::new(ia_file))?;
        tester.run_async(async move {
            // Key before the first LP
            let lp_a = packed_file.get_logical_partition(b"lp_a").await?;
            assert!(lp_a.is_none());

            // Key that should exist
            let lp_b = packed_file.get_logical_partition(b"lp_b").await?;
            assert!(lp_b.is_some());

            // Key between two existing LPs
            let lp_c = packed_file.get_logical_partition(b"lp_c").await?;
            assert!(lp_c.is_none());

            // Key that should exist
            let lp_d = packed_file.get_logical_partition(b"lp_d").await?;
            assert!(lp_d.is_some());

            // Key after the last LP
            let lp_e = packed_file.get_logical_partition(b"lp_e").await?;
            assert!(lp_e.is_none());

            Ok(())
        })
    }

    #[test]
    fn test_has_newer_version_invalid_pk_len() -> Result<()> {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        builder.start_lp(true, b"lp1")?; // int pk
        builder.add_pk_int(1, 100, false)?;
        builder.finish_lp()?;
        builder.finish()?;

        let tester = IaFileTester::default();
        let ia_file = tester.new_ia_file(
            FileType::FtsPackedFile,
            &buffer,
            get_packed_file_metadata_offset(&buffer),
        );
        let packed_file = PackedFile::new(Arc::new(ia_file))?;
        tester.run_async(async move {
            let lp1 = packed_file.get_logical_partition(b"lp1").await?.unwrap();
            assert!(lp1.is_pk_int());

            // Call with a PK of invalid length (not 8 bytes)
            let res = lp1.has_newer_version(b"short", 50, 150);
            assert!(res.is_err());
            assert!(
                res.unwrap_err()
                    .to_string()
                    .contains("Invalid int PK length")
            );
            Ok(())
        })
    }

    // TODO: Check smallest LP key and largest LP key
}
