[package]
name = "replication_worker"
version = "0.1.0"
edition = "2021"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[features]
testexport = []

[dependencies]
api_version = { workspace = true }
async-trait = "0.1"
bytes = "1.0"
cdc = { workspace = true }
chrono = "0.4"
dashmap = "5"
futures = "0.3"
grpcio = { workspace = true }
grpcio-health = { version = "0.10", default-features = false, features = ["protobuf-codec"] }
http = "0.2.9"
hyper = "0.14"
k8s-openapi = { version = "0.18.0", features = ["v1_26"] }
kube = { version = "0.84.0", features = ["runtime", "derive", "ws"] }
kvengine = { workspace = true }
kvproto = { workspace = true }
merged_engine = { workspace = true }
native_br = { workspace = true }
nix = "0.24"
pd_client = { workspace = true }
resolved_ts = { workspace = true }
rfengine = { workspace = true }
rfstore = { workspace = true }
security = { workspace = true }
semver = "0.10.0"
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_ignored = "0.1"
serde_json = "1.0"
slog = { version = "2.3", features = [
    "max_level_trace",
    "release_max_level_debug",
] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog-term = "2.4"
test_util = { workspace = true }
thiserror = "1.0"
tidb_query_datatype = { workspace = true }
tikv = { workspace = true }
tikv_kv = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.12", features = ["full"] }
txn_types = { workspace = true }
