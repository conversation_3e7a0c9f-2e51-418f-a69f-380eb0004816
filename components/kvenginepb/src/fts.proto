// Copyright 2025-present PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package enginepb;

message FullTextIndexDef {
  int64 index_id = 1;
  int64 col_id = 2;

  string parser_type = 10;
}

message PackedFileIndexBlock {
  repeated bytes data_block_start_keys = 1;

  // The absolute offset of the data block in the packed file.
  // It contains N+1 data blocks. The last offset is the end of the last data block.
  // Note that data block may contain padding bytes at the beginning.
  // For this reason, do not read data block from the beginning of the offset.
  repeated uint32 data_block_offsets = 2;
}

message PackedFilePropertyBlock {
  // Smallest LogicalPartitionKey in the file.
  // Used to quickly filter out LPs
  bytes smallest_lp_key = 1;
  // Largest LogicalPartitionKey in the file.
  // Used to quickly filter out LPs
  bytes largest_lp_key = 2;
  // Total number of logical partitions
  uint32 total_lps = 3;
  // Total number of keys (docs)
  uint32 total_keys = 4;
  // Total number of bytes of Tantivy indexes
  uint32 total_index_bytes = 5;
  // Total number of bytes of all Primary Keys
  uint32 total_keys_bytes = 6;
}

message FileRange {
    uint64 offset = 1;
    uint64 size = 2;
}

message Files {
    FileRange meta = 1;
    FileRange term = 2;
    FileRange idx = 3;
    FileRange pos = 4;
    FileRange store = 5;
    FileRange fast = 6;
    FileRange fieldnorm = 7;
}

message PropertyBlock {
    bytes lp_key = 1;
    bool is_int_handle = 2; // your spec had uint32, bool is more correct
    Files files = 10;
}

message HandleIndexBlock {
    // Sorted start keys of each handle block
    repeated bytes handle_block_start_key = 1;
    // Offsets for each handle block. If there are N blocks, there are N+1 offsets.
    // The i-th block is located at [offset[i], offset[i+1])).
    repeated uint64 handle_block_offset = 2;
}