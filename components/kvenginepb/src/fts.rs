// This file is generated by rust-protobuf 2.8.0. Do not edit
// @generated

// https://github.com/Manishearth/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![cfg_attr(rustfmt, rustfmt_skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unsafe_code)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `fts.proto`

use protobuf::Message as Message_imported_for_functions;
use protobuf::ProtobufEnum as ProtobufEnum_imported_for_functions;

/// Generated files are compatible only with the same version
/// of protobuf runtime.
const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_8_0;

#[derive(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,Default)]
pub struct FullTextIndexDef {
    // message fields
    pub index_id: i64,
    pub col_id: i64,
    pub parser_type: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a FullTextIndexDef {
    fn default() -> &'a FullTextIndexDef {
        <FullTextIndexDef as ::protobuf::Message>::default_instance()
    }
}

impl FullTextIndexDef {
    pub fn new() -> FullTextIndexDef {
        ::std::default::Default::default()
    }

    // int64 index_id = 1;


    pub fn get_index_id(&self) -> i64 {
        self.index_id
    }
    pub fn clear_index_id(&mut self) {
        self.index_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_index_id(&mut self, v: i64) {
        self.index_id = v;
    }

    // int64 col_id = 2;


    pub fn get_col_id(&self) -> i64 {
        self.col_id
    }
    pub fn clear_col_id(&mut self) {
        self.col_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_col_id(&mut self, v: i64) {
        self.col_id = v;
    }

    // string parser_type = 10;


    pub fn get_parser_type(&self) -> &str {
        &self.parser_type
    }
    pub fn clear_parser_type(&mut self) {
        self.parser_type.clear();
    }

    // Param is passed by value, moved
    pub fn set_parser_type(&mut self, v: ::std::string::String) {
        self.parser_type = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_parser_type(&mut self) -> &mut ::std::string::String {
        &mut self.parser_type
    }

    // Take field
    pub fn take_parser_type(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.parser_type, ::std::string::String::new())
    }
}

impl ::protobuf::Message for FullTextIndexDef {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.index_id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.col_id = tmp;
                },
                10 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.parser_type)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.index_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.index_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.col_id != 0 {
            my_size += ::protobuf::rt::value_size(2, self.col_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.parser_type.is_empty() {
            my_size += ::protobuf::rt::string_size(10, &self.parser_type);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.index_id != 0 {
            os.write_int64(1, self.index_id)?;
        }
        if self.col_id != 0 {
            os.write_int64(2, self.col_id)?;
        }
        if !self.parser_type.is_empty() {
            os.write_string(10, &self.parser_type)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> FullTextIndexDef {
        FullTextIndexDef::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "index_id",
                    |m: &FullTextIndexDef| { &m.index_id },
                    |m: &mut FullTextIndexDef| { &mut m.index_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "col_id",
                    |m: &FullTextIndexDef| { &m.col_id },
                    |m: &mut FullTextIndexDef| { &mut m.col_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                    "parser_type",
                    |m: &FullTextIndexDef| { &m.parser_type },
                    |m: &mut FullTextIndexDef| { &mut m.parser_type },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<FullTextIndexDef>(
                    "FullTextIndexDef",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static FullTextIndexDef {
        static mut instance: ::protobuf::lazy::Lazy<FullTextIndexDef> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const FullTextIndexDef,
        };
        unsafe {
            instance.get(FullTextIndexDef::new)
        }
    }
}

impl ::protobuf::Clear for FullTextIndexDef {
    fn clear(&mut self) {
        self.index_id = 0;
        self.col_id = 0;
        self.parser_type.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for FullTextIndexDef {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.index_id, "index_id", buf);
        ::protobuf::PbPrint::fmt(&self.col_id, "col_id", buf);
        ::protobuf::PbPrint::fmt(&self.parser_type, "parser_type", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for FullTextIndexDef {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.index_id, "index_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.col_id, "col_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.parser_type, "parser_type", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for FullTextIndexDef {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct PackedFileIndexBlock {
    // message fields
    pub data_block_start_keys: ::protobuf::RepeatedField<::std::vec::Vec<u8>>,
    pub data_block_offsets: ::std::vec::Vec<u32>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a PackedFileIndexBlock {
    fn default() -> &'a PackedFileIndexBlock {
        <PackedFileIndexBlock as ::protobuf::Message>::default_instance()
    }
}

impl PackedFileIndexBlock {
    pub fn new() -> PackedFileIndexBlock {
        ::std::default::Default::default()
    }

    // repeated bytes data_block_start_keys = 1;


    pub fn get_data_block_start_keys(&self) -> &[::std::vec::Vec<u8>] {
        &self.data_block_start_keys
    }
    pub fn clear_data_block_start_keys(&mut self) {
        self.data_block_start_keys.clear();
    }

    // Param is passed by value, moved
    pub fn set_data_block_start_keys(&mut self, v: ::protobuf::RepeatedField<::std::vec::Vec<u8>>) {
        self.data_block_start_keys = v;
    }

    // Mutable pointer to the field.
    pub fn mut_data_block_start_keys(&mut self) -> &mut ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        &mut self.data_block_start_keys
    }

    // Take field
    pub fn take_data_block_start_keys(&mut self) -> ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        ::std::mem::replace(&mut self.data_block_start_keys, ::protobuf::RepeatedField::new())
    }

    // repeated uint32 data_block_offsets = 2;


    pub fn get_data_block_offsets(&self) -> &[u32] {
        &self.data_block_offsets
    }
    pub fn clear_data_block_offsets(&mut self) {
        self.data_block_offsets.clear();
    }

    // Param is passed by value, moved
    pub fn set_data_block_offsets(&mut self, v: ::std::vec::Vec<u32>) {
        self.data_block_offsets = v;
    }

    // Mutable pointer to the field.
    pub fn mut_data_block_offsets(&mut self) -> &mut ::std::vec::Vec<u32> {
        &mut self.data_block_offsets
    }

    // Take field
    pub fn take_data_block_offsets(&mut self) -> ::std::vec::Vec<u32> {
        ::std::mem::replace(&mut self.data_block_offsets, ::std::vec::Vec::new())
    }
}

impl ::protobuf::Message for PackedFileIndexBlock {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_bytes_into(wire_type, is, &mut self.data_block_start_keys)?;
                },
                2 => {
                    ::protobuf::rt::read_repeated_uint32_into(wire_type, is, &mut self.data_block_offsets)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.data_block_start_keys {
            my_size += ::protobuf::rt::bytes_size(1, &value);
        };
        for value in &self.data_block_offsets {
            my_size += ::protobuf::rt::value_size(2, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        for v in &self.data_block_start_keys {
            os.write_bytes(1, &v)?;
        };
        for v in &self.data_block_offsets {
            os.write_uint32(2, *v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> PackedFileIndexBlock {
        PackedFileIndexBlock::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "data_block_start_keys",
                    |m: &PackedFileIndexBlock| { &m.data_block_start_keys },
                    |m: &mut PackedFileIndexBlock| { &mut m.data_block_start_keys },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "data_block_offsets",
                    |m: &PackedFileIndexBlock| { &m.data_block_offsets },
                    |m: &mut PackedFileIndexBlock| { &mut m.data_block_offsets },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<PackedFileIndexBlock>(
                    "PackedFileIndexBlock",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static PackedFileIndexBlock {
        static mut instance: ::protobuf::lazy::Lazy<PackedFileIndexBlock> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const PackedFileIndexBlock,
        };
        unsafe {
            instance.get(PackedFileIndexBlock::new)
        }
    }
}

impl ::protobuf::Clear for PackedFileIndexBlock {
    fn clear(&mut self) {
        self.data_block_start_keys.clear();
        self.data_block_offsets.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for PackedFileIndexBlock {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.data_block_start_keys, "data_block_start_keys", buf);
        ::protobuf::PbPrint::fmt(&self.data_block_offsets, "data_block_offsets", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for PackedFileIndexBlock {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.data_block_start_keys, "data_block_start_keys", &mut s);
        ::protobuf::PbPrint::fmt(&self.data_block_offsets, "data_block_offsets", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for PackedFileIndexBlock {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct PackedFilePropertyBlock {
    // message fields
    pub smallest_lp_key: ::std::vec::Vec<u8>,
    pub largest_lp_key: ::std::vec::Vec<u8>,
    pub total_lps: u32,
    pub total_keys: u32,
    pub total_index_bytes: u32,
    pub total_keys_bytes: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a PackedFilePropertyBlock {
    fn default() -> &'a PackedFilePropertyBlock {
        <PackedFilePropertyBlock as ::protobuf::Message>::default_instance()
    }
}

impl PackedFilePropertyBlock {
    pub fn new() -> PackedFilePropertyBlock {
        ::std::default::Default::default()
    }

    // bytes smallest_lp_key = 1;


    pub fn get_smallest_lp_key(&self) -> &[u8] {
        &self.smallest_lp_key
    }
    pub fn clear_smallest_lp_key(&mut self) {
        self.smallest_lp_key.clear();
    }

    // Param is passed by value, moved
    pub fn set_smallest_lp_key(&mut self, v: ::std::vec::Vec<u8>) {
        self.smallest_lp_key = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_smallest_lp_key(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.smallest_lp_key
    }

    // Take field
    pub fn take_smallest_lp_key(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.smallest_lp_key, ::std::vec::Vec::new())
    }

    // bytes largest_lp_key = 2;


    pub fn get_largest_lp_key(&self) -> &[u8] {
        &self.largest_lp_key
    }
    pub fn clear_largest_lp_key(&mut self) {
        self.largest_lp_key.clear();
    }

    // Param is passed by value, moved
    pub fn set_largest_lp_key(&mut self, v: ::std::vec::Vec<u8>) {
        self.largest_lp_key = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_largest_lp_key(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.largest_lp_key
    }

    // Take field
    pub fn take_largest_lp_key(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.largest_lp_key, ::std::vec::Vec::new())
    }

    // uint32 total_lps = 3;


    pub fn get_total_lps(&self) -> u32 {
        self.total_lps
    }
    pub fn clear_total_lps(&mut self) {
        self.total_lps = 0;
    }

    // Param is passed by value, moved
    pub fn set_total_lps(&mut self, v: u32) {
        self.total_lps = v;
    }

    // uint32 total_keys = 4;


    pub fn get_total_keys(&self) -> u32 {
        self.total_keys
    }
    pub fn clear_total_keys(&mut self) {
        self.total_keys = 0;
    }

    // Param is passed by value, moved
    pub fn set_total_keys(&mut self, v: u32) {
        self.total_keys = v;
    }

    // uint32 total_index_bytes = 5;


    pub fn get_total_index_bytes(&self) -> u32 {
        self.total_index_bytes
    }
    pub fn clear_total_index_bytes(&mut self) {
        self.total_index_bytes = 0;
    }

    // Param is passed by value, moved
    pub fn set_total_index_bytes(&mut self, v: u32) {
        self.total_index_bytes = v;
    }

    // uint32 total_keys_bytes = 6;


    pub fn get_total_keys_bytes(&self) -> u32 {
        self.total_keys_bytes
    }
    pub fn clear_total_keys_bytes(&mut self) {
        self.total_keys_bytes = 0;
    }

    // Param is passed by value, moved
    pub fn set_total_keys_bytes(&mut self, v: u32) {
        self.total_keys_bytes = v;
    }
}

impl ::protobuf::Message for PackedFilePropertyBlock {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.smallest_lp_key)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.largest_lp_key)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.total_lps = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.total_keys = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.total_index_bytes = tmp;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.total_keys_bytes = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.smallest_lp_key.is_empty() {
            my_size += ::protobuf::rt::bytes_size(1, &self.smallest_lp_key);
        }
        if !self.largest_lp_key.is_empty() {
            my_size += ::protobuf::rt::bytes_size(2, &self.largest_lp_key);
        }
        if self.total_lps != 0 {
            my_size += ::protobuf::rt::value_size(3, self.total_lps, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.total_keys != 0 {
            my_size += ::protobuf::rt::value_size(4, self.total_keys, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.total_index_bytes != 0 {
            my_size += ::protobuf::rt::value_size(5, self.total_index_bytes, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.total_keys_bytes != 0 {
            my_size += ::protobuf::rt::value_size(6, self.total_keys_bytes, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if !self.smallest_lp_key.is_empty() {
            os.write_bytes(1, &self.smallest_lp_key)?;
        }
        if !self.largest_lp_key.is_empty() {
            os.write_bytes(2, &self.largest_lp_key)?;
        }
        if self.total_lps != 0 {
            os.write_uint32(3, self.total_lps)?;
        }
        if self.total_keys != 0 {
            os.write_uint32(4, self.total_keys)?;
        }
        if self.total_index_bytes != 0 {
            os.write_uint32(5, self.total_index_bytes)?;
        }
        if self.total_keys_bytes != 0 {
            os.write_uint32(6, self.total_keys_bytes)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> PackedFilePropertyBlock {
        PackedFilePropertyBlock::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "smallest_lp_key",
                    |m: &PackedFilePropertyBlock| { &m.smallest_lp_key },
                    |m: &mut PackedFilePropertyBlock| { &mut m.smallest_lp_key },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "largest_lp_key",
                    |m: &PackedFilePropertyBlock| { &m.largest_lp_key },
                    |m: &mut PackedFilePropertyBlock| { &mut m.largest_lp_key },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "total_lps",
                    |m: &PackedFilePropertyBlock| { &m.total_lps },
                    |m: &mut PackedFilePropertyBlock| { &mut m.total_lps },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "total_keys",
                    |m: &PackedFilePropertyBlock| { &m.total_keys },
                    |m: &mut PackedFilePropertyBlock| { &mut m.total_keys },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "total_index_bytes",
                    |m: &PackedFilePropertyBlock| { &m.total_index_bytes },
                    |m: &mut PackedFilePropertyBlock| { &mut m.total_index_bytes },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "total_keys_bytes",
                    |m: &PackedFilePropertyBlock| { &m.total_keys_bytes },
                    |m: &mut PackedFilePropertyBlock| { &mut m.total_keys_bytes },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<PackedFilePropertyBlock>(
                    "PackedFilePropertyBlock",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static PackedFilePropertyBlock {
        static mut instance: ::protobuf::lazy::Lazy<PackedFilePropertyBlock> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const PackedFilePropertyBlock,
        };
        unsafe {
            instance.get(PackedFilePropertyBlock::new)
        }
    }
}

impl ::protobuf::Clear for PackedFilePropertyBlock {
    fn clear(&mut self) {
        self.smallest_lp_key.clear();
        self.largest_lp_key.clear();
        self.total_lps = 0;
        self.total_keys = 0;
        self.total_index_bytes = 0;
        self.total_keys_bytes = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for PackedFilePropertyBlock {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.smallest_lp_key, "smallest_lp_key", buf);
        ::protobuf::PbPrint::fmt(&self.largest_lp_key, "largest_lp_key", buf);
        ::protobuf::PbPrint::fmt(&self.total_lps, "total_lps", buf);
        ::protobuf::PbPrint::fmt(&self.total_keys, "total_keys", buf);
        ::protobuf::PbPrint::fmt(&self.total_index_bytes, "total_index_bytes", buf);
        ::protobuf::PbPrint::fmt(&self.total_keys_bytes, "total_keys_bytes", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for PackedFilePropertyBlock {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.smallest_lp_key, "smallest_lp_key", &mut s);
        ::protobuf::PbPrint::fmt(&self.largest_lp_key, "largest_lp_key", &mut s);
        ::protobuf::PbPrint::fmt(&self.total_lps, "total_lps", &mut s);
        ::protobuf::PbPrint::fmt(&self.total_keys, "total_keys", &mut s);
        ::protobuf::PbPrint::fmt(&self.total_index_bytes, "total_index_bytes", &mut s);
        ::protobuf::PbPrint::fmt(&self.total_keys_bytes, "total_keys_bytes", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for PackedFilePropertyBlock {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct FileRange {
    // message fields
    pub offset: u64,
    pub size: u64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a FileRange {
    fn default() -> &'a FileRange {
        <FileRange as ::protobuf::Message>::default_instance()
    }
}

impl FileRange {
    pub fn new() -> FileRange {
        ::std::default::Default::default()
    }

    // uint64 offset = 1;


    pub fn get_offset(&self) -> u64 {
        self.offset
    }
    pub fn clear_offset(&mut self) {
        self.offset = 0;
    }

    // Param is passed by value, moved
    pub fn set_offset(&mut self, v: u64) {
        self.offset = v;
    }

    // uint64 size = 2;


    pub fn get_size(&self) -> u64 {
        self.size
    }
    pub fn clear_size(&mut self) {
        self.size = 0;
    }

    // Param is passed by value, moved
    pub fn set_size(&mut self, v: u64) {
        self.size = v;
    }
}

impl ::protobuf::Message for FileRange {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.offset = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.size = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.offset != 0 {
            my_size += ::protobuf::rt::value_size(1, self.offset, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.size != 0 {
            my_size += ::protobuf::rt::value_size(2, self.size, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.offset != 0 {
            os.write_uint64(1, self.offset)?;
        }
        if self.size != 0 {
            os.write_uint64(2, self.size)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> FileRange {
        FileRange::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "offset",
                    |m: &FileRange| { &m.offset },
                    |m: &mut FileRange| { &mut m.offset },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "size",
                    |m: &FileRange| { &m.size },
                    |m: &mut FileRange| { &mut m.size },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<FileRange>(
                    "FileRange",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static FileRange {
        static mut instance: ::protobuf::lazy::Lazy<FileRange> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const FileRange,
        };
        unsafe {
            instance.get(FileRange::new)
        }
    }
}

impl ::protobuf::Clear for FileRange {
    fn clear(&mut self) {
        self.offset = 0;
        self.size = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for FileRange {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.offset, "offset", buf);
        ::protobuf::PbPrint::fmt(&self.size, "size", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for FileRange {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.offset, "offset", &mut s);
        ::protobuf::PbPrint::fmt(&self.size, "size", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for FileRange {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct Files {
    // message fields
    pub meta: ::protobuf::SingularPtrField<FileRange>,
    pub term: ::protobuf::SingularPtrField<FileRange>,
    pub idx: ::protobuf::SingularPtrField<FileRange>,
    pub pos: ::protobuf::SingularPtrField<FileRange>,
    pub store: ::protobuf::SingularPtrField<FileRange>,
    pub fast: ::protobuf::SingularPtrField<FileRange>,
    pub fieldnorm: ::protobuf::SingularPtrField<FileRange>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a Files {
    fn default() -> &'a Files {
        <Files as ::protobuf::Message>::default_instance()
    }
}

impl Files {
    pub fn new() -> Files {
        ::std::default::Default::default()
    }

    // .enginepb.FileRange meta = 1;


    pub fn get_meta(&self) -> &FileRange {
        self.meta.as_ref().unwrap_or_else(|| FileRange::default_instance())
    }
    pub fn clear_meta(&mut self) {
        self.meta.clear();
    }

    pub fn has_meta(&self) -> bool {
        self.meta.is_some()
    }

    // Param is passed by value, moved
    pub fn set_meta(&mut self, v: FileRange) {
        self.meta = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_meta(&mut self) -> &mut FileRange {
        if self.meta.is_none() {
            self.meta.set_default();
        }
        self.meta.as_mut().unwrap()
    }

    // Take field
    pub fn take_meta(&mut self) -> FileRange {
        self.meta.take().unwrap_or_else(|| FileRange::new())
    }

    // .enginepb.FileRange term = 2;


    pub fn get_term(&self) -> &FileRange {
        self.term.as_ref().unwrap_or_else(|| FileRange::default_instance())
    }
    pub fn clear_term(&mut self) {
        self.term.clear();
    }

    pub fn has_term(&self) -> bool {
        self.term.is_some()
    }

    // Param is passed by value, moved
    pub fn set_term(&mut self, v: FileRange) {
        self.term = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_term(&mut self) -> &mut FileRange {
        if self.term.is_none() {
            self.term.set_default();
        }
        self.term.as_mut().unwrap()
    }

    // Take field
    pub fn take_term(&mut self) -> FileRange {
        self.term.take().unwrap_or_else(|| FileRange::new())
    }

    // .enginepb.FileRange idx = 3;


    pub fn get_idx(&self) -> &FileRange {
        self.idx.as_ref().unwrap_or_else(|| FileRange::default_instance())
    }
    pub fn clear_idx(&mut self) {
        self.idx.clear();
    }

    pub fn has_idx(&self) -> bool {
        self.idx.is_some()
    }

    // Param is passed by value, moved
    pub fn set_idx(&mut self, v: FileRange) {
        self.idx = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_idx(&mut self) -> &mut FileRange {
        if self.idx.is_none() {
            self.idx.set_default();
        }
        self.idx.as_mut().unwrap()
    }

    // Take field
    pub fn take_idx(&mut self) -> FileRange {
        self.idx.take().unwrap_or_else(|| FileRange::new())
    }

    // .enginepb.FileRange pos = 4;


    pub fn get_pos(&self) -> &FileRange {
        self.pos.as_ref().unwrap_or_else(|| FileRange::default_instance())
    }
    pub fn clear_pos(&mut self) {
        self.pos.clear();
    }

    pub fn has_pos(&self) -> bool {
        self.pos.is_some()
    }

    // Param is passed by value, moved
    pub fn set_pos(&mut self, v: FileRange) {
        self.pos = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_pos(&mut self) -> &mut FileRange {
        if self.pos.is_none() {
            self.pos.set_default();
        }
        self.pos.as_mut().unwrap()
    }

    // Take field
    pub fn take_pos(&mut self) -> FileRange {
        self.pos.take().unwrap_or_else(|| FileRange::new())
    }

    // .enginepb.FileRange store = 5;


    pub fn get_store(&self) -> &FileRange {
        self.store.as_ref().unwrap_or_else(|| FileRange::default_instance())
    }
    pub fn clear_store(&mut self) {
        self.store.clear();
    }

    pub fn has_store(&self) -> bool {
        self.store.is_some()
    }

    // Param is passed by value, moved
    pub fn set_store(&mut self, v: FileRange) {
        self.store = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_store(&mut self) -> &mut FileRange {
        if self.store.is_none() {
            self.store.set_default();
        }
        self.store.as_mut().unwrap()
    }

    // Take field
    pub fn take_store(&mut self) -> FileRange {
        self.store.take().unwrap_or_else(|| FileRange::new())
    }

    // .enginepb.FileRange fast = 6;


    pub fn get_fast(&self) -> &FileRange {
        self.fast.as_ref().unwrap_or_else(|| FileRange::default_instance())
    }
    pub fn clear_fast(&mut self) {
        self.fast.clear();
    }

    pub fn has_fast(&self) -> bool {
        self.fast.is_some()
    }

    // Param is passed by value, moved
    pub fn set_fast(&mut self, v: FileRange) {
        self.fast = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_fast(&mut self) -> &mut FileRange {
        if self.fast.is_none() {
            self.fast.set_default();
        }
        self.fast.as_mut().unwrap()
    }

    // Take field
    pub fn take_fast(&mut self) -> FileRange {
        self.fast.take().unwrap_or_else(|| FileRange::new())
    }

    // .enginepb.FileRange fieldnorm = 7;


    pub fn get_fieldnorm(&self) -> &FileRange {
        self.fieldnorm.as_ref().unwrap_or_else(|| FileRange::default_instance())
    }
    pub fn clear_fieldnorm(&mut self) {
        self.fieldnorm.clear();
    }

    pub fn has_fieldnorm(&self) -> bool {
        self.fieldnorm.is_some()
    }

    // Param is passed by value, moved
    pub fn set_fieldnorm(&mut self, v: FileRange) {
        self.fieldnorm = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_fieldnorm(&mut self) -> &mut FileRange {
        if self.fieldnorm.is_none() {
            self.fieldnorm.set_default();
        }
        self.fieldnorm.as_mut().unwrap()
    }

    // Take field
    pub fn take_fieldnorm(&mut self) -> FileRange {
        self.fieldnorm.take().unwrap_or_else(|| FileRange::new())
    }
}

impl ::protobuf::Message for Files {
    fn is_initialized(&self) -> bool {
        for v in &self.meta {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.term {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.idx {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.pos {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.store {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.fast {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.fieldnorm {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.meta)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.term)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.idx)?;
                },
                4 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.pos)?;
                },
                5 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.store)?;
                },
                6 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.fast)?;
                },
                7 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.fieldnorm)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.meta.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.term.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.idx.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.pos.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.store.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.fast.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.fieldnorm.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.meta.as_ref() {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.term.as_ref() {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.idx.as_ref() {
            os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.pos.as_ref() {
            os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.store.as_ref() {
            os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.fast.as_ref() {
            os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.fieldnorm.as_ref() {
            os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> Files {
        Files::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<FileRange>>(
                    "meta",
                    |m: &Files| { &m.meta },
                    |m: &mut Files| { &mut m.meta },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<FileRange>>(
                    "term",
                    |m: &Files| { &m.term },
                    |m: &mut Files| { &mut m.term },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<FileRange>>(
                    "idx",
                    |m: &Files| { &m.idx },
                    |m: &mut Files| { &mut m.idx },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<FileRange>>(
                    "pos",
                    |m: &Files| { &m.pos },
                    |m: &mut Files| { &mut m.pos },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<FileRange>>(
                    "store",
                    |m: &Files| { &m.store },
                    |m: &mut Files| { &mut m.store },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<FileRange>>(
                    "fast",
                    |m: &Files| { &m.fast },
                    |m: &mut Files| { &mut m.fast },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<FileRange>>(
                    "fieldnorm",
                    |m: &Files| { &m.fieldnorm },
                    |m: &mut Files| { &mut m.fieldnorm },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<Files>(
                    "Files",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static Files {
        static mut instance: ::protobuf::lazy::Lazy<Files> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const Files,
        };
        unsafe {
            instance.get(Files::new)
        }
    }
}

impl ::protobuf::Clear for Files {
    fn clear(&mut self) {
        self.meta.clear();
        self.term.clear();
        self.idx.clear();
        self.pos.clear();
        self.store.clear();
        self.fast.clear();
        self.fieldnorm.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for Files {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.meta, "meta", buf);
        ::protobuf::PbPrint::fmt(&self.term, "term", buf);
        ::protobuf::PbPrint::fmt(&self.idx, "idx", buf);
        ::protobuf::PbPrint::fmt(&self.pos, "pos", buf);
        ::protobuf::PbPrint::fmt(&self.store, "store", buf);
        ::protobuf::PbPrint::fmt(&self.fast, "fast", buf);
        ::protobuf::PbPrint::fmt(&self.fieldnorm, "fieldnorm", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for Files {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.meta, "meta", &mut s);
        ::protobuf::PbPrint::fmt(&self.term, "term", &mut s);
        ::protobuf::PbPrint::fmt(&self.idx, "idx", &mut s);
        ::protobuf::PbPrint::fmt(&self.pos, "pos", &mut s);
        ::protobuf::PbPrint::fmt(&self.store, "store", &mut s);
        ::protobuf::PbPrint::fmt(&self.fast, "fast", &mut s);
        ::protobuf::PbPrint::fmt(&self.fieldnorm, "fieldnorm", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for Files {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct PropertyBlock {
    // message fields
    pub lp_key: ::std::vec::Vec<u8>,
    pub is_int_handle: bool,
    pub files: ::protobuf::SingularPtrField<Files>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a PropertyBlock {
    fn default() -> &'a PropertyBlock {
        <PropertyBlock as ::protobuf::Message>::default_instance()
    }
}

impl PropertyBlock {
    pub fn new() -> PropertyBlock {
        ::std::default::Default::default()
    }

    // bytes lp_key = 1;


    pub fn get_lp_key(&self) -> &[u8] {
        &self.lp_key
    }
    pub fn clear_lp_key(&mut self) {
        self.lp_key.clear();
    }

    // Param is passed by value, moved
    pub fn set_lp_key(&mut self, v: ::std::vec::Vec<u8>) {
        self.lp_key = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_lp_key(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.lp_key
    }

    // Take field
    pub fn take_lp_key(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.lp_key, ::std::vec::Vec::new())
    }

    // bool is_int_handle = 2;


    pub fn get_is_int_handle(&self) -> bool {
        self.is_int_handle
    }
    pub fn clear_is_int_handle(&mut self) {
        self.is_int_handle = false;
    }

    // Param is passed by value, moved
    pub fn set_is_int_handle(&mut self, v: bool) {
        self.is_int_handle = v;
    }

    // .enginepb.Files files = 10;


    pub fn get_files(&self) -> &Files {
        self.files.as_ref().unwrap_or_else(|| Files::default_instance())
    }
    pub fn clear_files(&mut self) {
        self.files.clear();
    }

    pub fn has_files(&self) -> bool {
        self.files.is_some()
    }

    // Param is passed by value, moved
    pub fn set_files(&mut self, v: Files) {
        self.files = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_files(&mut self) -> &mut Files {
        if self.files.is_none() {
            self.files.set_default();
        }
        self.files.as_mut().unwrap()
    }

    // Take field
    pub fn take_files(&mut self) -> Files {
        self.files.take().unwrap_or_else(|| Files::new())
    }
}

impl ::protobuf::Message for PropertyBlock {
    fn is_initialized(&self) -> bool {
        for v in &self.files {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.lp_key)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.is_int_handle = tmp;
                },
                10 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.files)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.lp_key.is_empty() {
            my_size += ::protobuf::rt::bytes_size(1, &self.lp_key);
        }
        if self.is_int_handle != false {
            my_size += 2;
        }
        if let Some(ref v) = self.files.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if !self.lp_key.is_empty() {
            os.write_bytes(1, &self.lp_key)?;
        }
        if self.is_int_handle != false {
            os.write_bool(2, self.is_int_handle)?;
        }
        if let Some(ref v) = self.files.as_ref() {
            os.write_tag(10, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> PropertyBlock {
        PropertyBlock::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "lp_key",
                    |m: &PropertyBlock| { &m.lp_key },
                    |m: &mut PropertyBlock| { &mut m.lp_key },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                    "is_int_handle",
                    |m: &PropertyBlock| { &m.is_int_handle },
                    |m: &mut PropertyBlock| { &mut m.is_int_handle },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Files>>(
                    "files",
                    |m: &PropertyBlock| { &m.files },
                    |m: &mut PropertyBlock| { &mut m.files },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<PropertyBlock>(
                    "PropertyBlock",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static PropertyBlock {
        static mut instance: ::protobuf::lazy::Lazy<PropertyBlock> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const PropertyBlock,
        };
        unsafe {
            instance.get(PropertyBlock::new)
        }
    }
}

impl ::protobuf::Clear for PropertyBlock {
    fn clear(&mut self) {
        self.lp_key.clear();
        self.is_int_handle = false;
        self.files.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for PropertyBlock {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.lp_key, "lp_key", buf);
        ::protobuf::PbPrint::fmt(&self.is_int_handle, "is_int_handle", buf);
        ::protobuf::PbPrint::fmt(&self.files, "files", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for PropertyBlock {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.lp_key, "lp_key", &mut s);
        ::protobuf::PbPrint::fmt(&self.is_int_handle, "is_int_handle", &mut s);
        ::protobuf::PbPrint::fmt(&self.files, "files", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for PropertyBlock {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct HandleIndexBlock {
    // message fields
    pub handle_block_start_key: ::protobuf::RepeatedField<::std::vec::Vec<u8>>,
    pub handle_block_offset: ::std::vec::Vec<u64>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a HandleIndexBlock {
    fn default() -> &'a HandleIndexBlock {
        <HandleIndexBlock as ::protobuf::Message>::default_instance()
    }
}

impl HandleIndexBlock {
    pub fn new() -> HandleIndexBlock {
        ::std::default::Default::default()
    }

    // repeated bytes handle_block_start_key = 1;


    pub fn get_handle_block_start_key(&self) -> &[::std::vec::Vec<u8>] {
        &self.handle_block_start_key
    }
    pub fn clear_handle_block_start_key(&mut self) {
        self.handle_block_start_key.clear();
    }

    // Param is passed by value, moved
    pub fn set_handle_block_start_key(&mut self, v: ::protobuf::RepeatedField<::std::vec::Vec<u8>>) {
        self.handle_block_start_key = v;
    }

    // Mutable pointer to the field.
    pub fn mut_handle_block_start_key(&mut self) -> &mut ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        &mut self.handle_block_start_key
    }

    // Take field
    pub fn take_handle_block_start_key(&mut self) -> ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        ::std::mem::replace(&mut self.handle_block_start_key, ::protobuf::RepeatedField::new())
    }

    // repeated uint64 handle_block_offset = 2;


    pub fn get_handle_block_offset(&self) -> &[u64] {
        &self.handle_block_offset
    }
    pub fn clear_handle_block_offset(&mut self) {
        self.handle_block_offset.clear();
    }

    // Param is passed by value, moved
    pub fn set_handle_block_offset(&mut self, v: ::std::vec::Vec<u64>) {
        self.handle_block_offset = v;
    }

    // Mutable pointer to the field.
    pub fn mut_handle_block_offset(&mut self) -> &mut ::std::vec::Vec<u64> {
        &mut self.handle_block_offset
    }

    // Take field
    pub fn take_handle_block_offset(&mut self) -> ::std::vec::Vec<u64> {
        ::std::mem::replace(&mut self.handle_block_offset, ::std::vec::Vec::new())
    }
}

impl ::protobuf::Message for HandleIndexBlock {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_bytes_into(wire_type, is, &mut self.handle_block_start_key)?;
                },
                2 => {
                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.handle_block_offset)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.handle_block_start_key {
            my_size += ::protobuf::rt::bytes_size(1, &value);
        };
        for value in &self.handle_block_offset {
            my_size += ::protobuf::rt::value_size(2, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        for v in &self.handle_block_start_key {
            os.write_bytes(1, &v)?;
        };
        for v in &self.handle_block_offset {
            os.write_uint64(2, *v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> HandleIndexBlock {
        HandleIndexBlock::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "handle_block_start_key",
                    |m: &HandleIndexBlock| { &m.handle_block_start_key },
                    |m: &mut HandleIndexBlock| { &mut m.handle_block_start_key },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "handle_block_offset",
                    |m: &HandleIndexBlock| { &m.handle_block_offset },
                    |m: &mut HandleIndexBlock| { &mut m.handle_block_offset },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<HandleIndexBlock>(
                    "HandleIndexBlock",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static HandleIndexBlock {
        static mut instance: ::protobuf::lazy::Lazy<HandleIndexBlock> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const HandleIndexBlock,
        };
        unsafe {
            instance.get(HandleIndexBlock::new)
        }
    }
}

impl ::protobuf::Clear for HandleIndexBlock {
    fn clear(&mut self) {
        self.handle_block_start_key.clear();
        self.handle_block_offset.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for HandleIndexBlock {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.handle_block_start_key, "handle_block_start_key", buf);
        ::protobuf::PbPrint::fmt(&self.handle_block_offset, "handle_block_offset", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for HandleIndexBlock {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.handle_block_start_key, "handle_block_start_key", &mut s);
        ::protobuf::PbPrint::fmt(&self.handle_block_offset, "handle_block_offset", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for HandleIndexBlock {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\tfts.proto\x12\x08enginepb\"Q\n\x10FullTextIndexDef\x12\x12\n\x08inde\
    x_id\x18\x01\x20\x01(\x03B\0\x12\x10\n\x06col_id\x18\x02\x20\x01(\x03B\0\
    \x12\x15\n\x0bparser_type\x18\n\x20\x01(\tB\0:\0\"W\n\x14PackedFileIndex\
    Block\x12\x1f\n\x15data_block_start_keys\x18\x01\x20\x03(\x0cB\0\x12\x1c\
    \n\x12data_block_offsets\x18\x02\x20\x03(\rB\0:\0\"\xb4\x01\n\x17PackedF\
    ilePropertyBlock\x12\x19\n\x0fsmallest_lp_key\x18\x01\x20\x01(\x0cB\0\
    \x12\x18\n\x0elargest_lp_key\x18\x02\x20\x01(\x0cB\0\x12\x13\n\ttotal_lp\
    s\x18\x03\x20\x01(\rB\0\x12\x14\n\ntotal_keys\x18\x04\x20\x01(\rB\0\x12\
    \x1b\n\x11total_index_bytes\x18\x05\x20\x01(\rB\0\x12\x1a\n\x10total_key\
    s_bytes\x18\x06\x20\x01(\rB\0:\0\"/\n\tFileRange\x12\x10\n\x06offset\x18\
    \x01\x20\x01(\x04B\0\x12\x0e\n\x04size\x18\x02\x20\x01(\x04B\0:\0\"\x90\
    \x02\n\x05Files\x12#\n\x04meta\x18\x01\x20\x01(\x0b2\x13.enginepb.FileRa\
    ngeB\0\x12#\n\x04term\x18\x02\x20\x01(\x0b2\x13.enginepb.FileRangeB\0\
    \x12\"\n\x03idx\x18\x03\x20\x01(\x0b2\x13.enginepb.FileRangeB\0\x12\"\n\
    \x03pos\x18\x04\x20\x01(\x0b2\x13.enginepb.FileRangeB\0\x12$\n\x05store\
    \x18\x05\x20\x01(\x0b2\x13.enginepb.FileRangeB\0\x12#\n\x04fast\x18\x06\
    \x20\x01(\x0b2\x13.enginepb.FileRangeB\0\x12(\n\tfieldnorm\x18\x07\x20\
    \x01(\x0b2\x13.enginepb.FileRangeB\0:\0\"^\n\rPropertyBlock\x12\x10\n\
    \x06lp_key\x18\x01\x20\x01(\x0cB\0\x12\x17\n\ris_int_handle\x18\x02\x20\
    \x01(\x08B\0\x12\x20\n\x05files\x18\n\x20\x01(\x0b2\x0f.enginepb.FilesB\
    \0:\0\"U\n\x10HandleIndexBlock\x12\x20\n\x16handle_block_start_key\x18\
    \x01\x20\x03(\x0cB\0\x12\x1d\n\x13handle_block_offset\x18\x02\x20\x03(\
    \x04B\0:\0B\0b\x06proto3\
";

static mut file_descriptor_proto_lazy: ::protobuf::lazy::Lazy<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::lazy::Lazy {
    lock: ::protobuf::lazy::ONCE_INIT,
    ptr: 0 as *const ::protobuf::descriptor::FileDescriptorProto,
};

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    unsafe {
        file_descriptor_proto_lazy.get(|| {
            parse_descriptor_proto()
        })
    }
}
