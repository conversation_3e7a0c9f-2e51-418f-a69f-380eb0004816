[package]
name = "snap_recovery"
version = "0.1.0"
edition = "2021"
publish = false
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
chrono = "0.4"
encryption = { workspace = true }
encryption_export = { workspace = true }
engine_rocks = { workspace = true }
engine_traits = { workspace = true }
futures = { version = "0.3", features = ["executor"] }
grpcio = { workspace = true }
keys = { workspace = true }
kvproto = { workspace = true }
log = { version = "0.4", features = ["max_level_trace", "release_max_level_debug"] }
pd_client = { workspace = true }
protobuf = { version = "2.8", features = ["bytes"] }
raft_log_engine = { workspace = true }
raftstore = { workspace = true }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
structopt = "0.3"
tempfile = "3.0"
thiserror = "1.0"
tikv = { workspace = true }
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
toml = "0.5"
txn_types = { workspace = true }
