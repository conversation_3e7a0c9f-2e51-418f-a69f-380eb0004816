[package]
name = "test_cloud_server"
version = "0.1.0"
edition = "2021"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[features]
testexport = []
debug-trace-txn-tasks = ["tikv/debug-trace-txn-tasks"]

[dependencies]
anyhow = "1.0"
api_version = { path = "../api_version" }
async-stream = "0.2"
bstr = "0.2.17"
builtin_dfs = { path = "../builtin_dfs" }
bytes = "1"
chrono = "0.4"
cloud_server = { path = "../cloud_server" }
cloud_worker = { path = "../cloud_worker", features = ["testexport"] }
codec = { workspace = true }
crc32c = { workspace = true }
crc32fast = "1.4.0"
dashmap = "4.0"
engine_traits = { path = "../engine_traits", default-features = false }
fail = "0.5"
file_system = { path = "../file_system" }
futures = "0.3"
glob = "0.3"
grpcio = { version = "0.10", default-features = false, features = ["openssl-vendored", "protobuf-codec"] }
hex = "0.4"
hyper = { version = "0.14", features = ["full"] }
kvengine = { path = "../kvengine", features = ["testexport"] }
kvproto = { workspace = true }
lazy_static = "1.3"
libc = "0.2"
load_data = { path = "../load_data" }
log_wrappers = { path = "../log_wrappers" }
nix = "0.24"
parking_lot = "0.12"
pd_client = { path = "../pd_client", default-features = false, features = ["testexport"] }
protobuf = { version = "2.8", features = ["bytes"] }
quick-xml = { version = "0.23.1", features = ["serialize"] }
rand = "0.8"
regex = "1.5"
reqwest = { version = "0.11", default-features = false, features = ["blocking", "json"] }
rfengine = { path = "../rfengine" }
rfstore = { path = "../rfstore" }
rstest = "0.18"
schema = { workspace = true }
security = { path = "../security", default-features = false }
serde = { version = "1.0", features = ["derive"] }
serde_derive = "1.0"
serde_json = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tempfile = "3.0"
test_pd = { path = "../test_pd" }
test_pd_client = { path = "../test_pd_client" }
test_raftstore = { path = "../test_raftstore" }
test_util = { path = "../test_util" }
thiserror = "1.0"
tidb_query_common = { workspace = true }
tidb_query_datatype = { workspace = true }
tikv = { path = "../.." }
tikv-client = { workspace = true }
tikv_alloc = { workspace = true }
tikv_util = { path = "../tikv_util" }
tipb = { workspace = true }
tokio = { version = "1", features = ["full"] }
tokio-util = "0.7"
toml = "0.5"
url = "2"
zipf = "7.0"

[dev-dependencies]
collections = { path = "../collections" }
