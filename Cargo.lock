# This file is automatically @generated by Car<PERSON>.
# It is not intended for manual editing.
version = 3

[[package]]
name = "RustyXML"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b5ace29ee3216de37c0546865ad08edef58b0f9e76838ed8959a84a990e58c5"

[[package]]
name = "addr2line"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e61f2b7f93d2c7d2b08263acaa4a363b3e276806c68af6134c44f523bf1aacd"
dependencies = [
 "gimli",
]

[[package]]
name = "adler"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f26201604c87b1e01bd3d98f8d5d9a8fcbb815e8cedb41ffccbeb4bf593a35fe"

[[package]]
name = "adler2"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "320119579fcad9c21884f5c4861d16174d0e06250625266f50fe6898340abefa"

[[package]]
name = "adler32"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aae1277d39aeec15cb388266ecc24b11c80469deae6067e17a1a7aa9e5c1f234"

[[package]]
name = "afl"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59206260f98d163b3ca42fb29fe551dbcda1d43cf70a244066b2a0666a8fb2a9"
dependencies = [
 "cc",
 "clap 2.34.0",
 "rustc_version 0.2.3",
 "xdg",
]

[[package]]
name = "ahash"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "891477e0c6a8957309ee5c45a6368af3ae14bb510732d2684ffa19af310920f9"
dependencies = [
 "getrandom 0.2.16",
 "once_cell",
 "version_check 0.9.4",
]

[[package]]
name = "ahash"
version = "0.8.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e89da841a80418a9b391ebaea17f5c112ffaaa96f621d2c285b5174da76b9011"
dependencies = [
 "cfg-if 1.0.0",
 "getrandom 0.2.16",
 "once_cell",
 "version_check 0.9.4",
 "zerocopy 0.7.35",
]

[[package]]
name = "aho-corasick"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e60d3430d3a69478ad0993f19238d2df97c507009a52b3c10addcd7f6bcb916"
dependencies = [
 "memchr",
]

[[package]]
name = "aligned-vec"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e0966165eaf052580bd70eb1b32cb3d6245774c0104d1b2793e9650bf83b52a"
dependencies = [
 "equator",
]

[[package]]
name = "aliyun"
version = "0.1.0"
dependencies = [
 "async-trait",
 "aws",
 "base64 0.13.0",
 "chrono",
 "cloud_encryption",
 "http",
 "hyper",
 "hyper-tls",
 "rusoto_core",
 "rusoto_credential",
 "rust-crypto",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
]

[[package]]
name = "allocator-api2"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "683d7910e743518b0e34f1186f92494becacb047c7b6bf616c96772180fef923"

[[package]]
name = "android-tzdata"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e999941b234f3131b00bc13c22d06e8c5ff726d1b6318ac7eb276997bbb4fef0"

[[package]]
name = "android_system_properties"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "ansi_term"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d52a9bb7ec0cf484c551830a7ce27bd20d67eac647e1befb56b0be4ee39a55d2"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "anyhow"
version = "1.0.98"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e16d2d3311acee920a9eb8d33b8cbc1787ce4a264e85f964c2404b969bdcd487"

[[package]]
name = "api_version"
version = "0.1.0"
dependencies = [
 "bitflags 1.3.2",
 "bytes",
 "codec",
 "engine_traits",
 "hex 0.4.3",
 "kvproto",
 "match-template",
 "panic_hook",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "txn_types",
]

[[package]]
name = "arbitrary"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db55d72333851e17d572bec876e390cd3b11eb1ef53ae821dd9f3b653d2b4569"

[[package]]
name = "arbitrary"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e90af4de65aa7b293ef2d09daff88501eb254f58edde2e1ac02c82d873eadad"

[[package]]
name = "arc-swap"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dabe5a181f83789739c194cbe5a897dde195078fac08568d09221fd6137a7ba8"

[[package]]
name = "arc-swap"
version = "1.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69f7f8c3906b62b754cd5326047894316021dcfe5a194c8ea52bdd94934a3457"

[[package]]
name = "arrayvec"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c02d123df017efcdfbd739ef81735b36c5ba83ec3c59c80a9d7ecc718f92e50"

[[package]]
name = "arrow"
version = "13.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c6bee230122beb516ead31935a61f683715f987c6f003eff44ad6986624105a"
dependencies = [
 "bitflags 1.3.2",
 "chrono",
 "csv",
 "flatbuffers",
 "half 1.8.2",
 "hex 0.4.3",
 "indexmap 1.6.2",
 "lazy_static",
 "lexical-core",
 "multiversion",
 "num 0.4.0",
 "rand 0.8.5",
 "regex",
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "arrow-buffer"
version = "53.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a1f34f0faae77da6b142db61deba2cb6d60167592b178be317b341440acba80"
dependencies = [
 "bytes",
 "half 2.4.1",
 "num 0.4.0",
]

[[package]]
name = "async-channel"
version = "1.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2114d64672151c0c5eaa5e131ec84a74f06e1e559830dabba01ca30605d66319"
dependencies = [
 "concurrent-queue",
 "event-listener",
 "futures-core",
]

[[package]]
name = "async-compression"
version = "0.3.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "345fd392ab01f746c717b1357165b76f0b67a60192007b234058c9045fdcf695"
dependencies = [
 "futures-core",
 "futures-io",
 "memchr",
 "pin-project-lite",
 "tokio",
 "zstd 0.11.2+zstd.1.5.2",
 "zstd-safe 5.0.2+zstd.1.5.2",
]

[[package]]
name = "async-recursion"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d7d78656ba01f1b93024b7c3a0467f1608e4be67d725749fdcd7d2c7678fd7a2"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "async-recursion"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b43422f69d8ff38f95f1b2bb76517c91589a924d1559a0e935d7c8ce0274c11"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "async-speed-limit"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "481ce9cb6a828f4679495f7376cb6779978d925dd9790b99b48d1bbde6d0f00b"
dependencies = [
 "futures-core",
 "futures-io",
 "futures-timer",
 "pin-project-lite",
]

[[package]]
name = "async-stream"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58982858be7540a465c790b95aaea6710e5139bf8956b1d1344d014fa40100b0"
dependencies = [
 "async-stream-impl 0.2.0",
 "futures-core",
]

[[package]]
name = "async-stream"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dad5c83079eae9969be7fadefe640a1c566901f05ff91ab221de4b6f68d9507e"
dependencies = [
 "async-stream-impl 0.3.3",
 "futures-core",
]

[[package]]
name = "async-stream-impl"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "393356ed99aa7bff0ac486dde592633b83ab02bd254d8c209d5b9f1d0f533480"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "async-stream-impl"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10f203db73a71dfa2fb6dd22763990fa26f3d2625a6da2da900d23b87d26be27"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "async-timer"
version = "1.0.0-beta.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d962799a5863fdf06fbf594e04102130582d010379137e9a98a7e2e693a5885"
dependencies = [
 "error-code",
 "libc 0.2.174",
 "wasm-bindgen",
 "winapi 0.3.9",
]

[[package]]
name = "async-trait"
version = "0.1.68"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9ccdd8f2a161be9bd5c023df56f1b2a0bd1d83872ae53b71a84a12c9bf6e842"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "atoi"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "616896e05fc0e2649463a93a15183c6a16bf03413a7af88ef1285ddedfa9cda5"
dependencies = [
 "num-traits",
]

[[package]]
name = "atomic"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3410529e8288c463bedb5930f82833bc0c90e5d2fe639a56582a4d09220b281"
dependencies = [
 "autocfg",
]

[[package]]
name = "atty"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1803c647a3ec87095e7ae7acfca019e98de5ec9a7d01343f611cf3152ed71a90"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "autocfg"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d468802bab17cbc0cc575e9b053f41e72aa36bfa6b7f55e3529ffa43161b97fa"

[[package]]
name = "autotools"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef941527c41b0fc0dd48511a8154cd5fc7e29200a0ff8b7203c5d777dbc795cf"
dependencies = [
 "cc",
]

[[package]]
name = "aws"
version = "0.0.1"
dependencies = [
 "async-trait",
 "base64 0.13.0",
 "bytes",
 "chrono",
 "cloud",
 "fail 0.5.0",
 "futures 0.3.28",
 "futures-util",
 "grpcio",
 "http",
 "hyper",
 "hyper-tls",
 "kvproto",
 "lazy_static",
 "md5",
 "prometheus",
 "rusoto_core",
 "rusoto_credential",
 "rusoto_kms",
 "rusoto_mock",
 "rusoto_s3",
 "rusoto_sts",
 "slog",
 "slog-global",
 "thiserror",
 "tikv_util",
 "tokio",
 "url",
 "uuid 0.8.2",
]

[[package]]
name = "axum"
version = "0.5.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acee9fd5073ab6b045a275b3e709c163dd36c90685219cb21804a147b58dba43"
dependencies = [
 "async-trait",
 "axum-core 0.2.9",
 "bitflags 1.3.2",
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "hyper",
 "itoa 1.0.6",
 "matchit 0.5.0",
 "memchr",
 "mime",
 "percent-encoding",
 "pin-project-lite",
 "serde",
 "sync_wrapper",
 "tokio",
 "tower",
 "tower-http 0.3.5",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum"
version = "0.6.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8175979259124331c1d7bf6586ee7e0da434155e4b2d48ec2c8386281d8df39"
dependencies = [
 "async-trait",
 "axum-core 0.3.4",
 "bitflags 1.3.2",
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "hyper",
 "itoa 1.0.6",
 "matchit 0.7.0",
 "memchr",
 "mime",
 "percent-encoding",
 "pin-project-lite",
 "rustversion",
 "serde",
 "sync_wrapper",
 "tower",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-core"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37e5939e02c56fecd5c017c37df4238c0a839fa76b7f97acdd7efb804fd181cc"
dependencies = [
 "async-trait",
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "mime",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-core"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "759fa577a247914fd3f7f76d62972792636412fbfd634cd452f6a385a74d2d2c"
dependencies = [
 "async-trait",
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "mime",
 "rustversion",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "azure"
version = "0.0.1"
dependencies = [
 "async-trait",
 "azure_core",
 "azure_identity",
 "azure_storage",
 "base64 0.13.0",
 "chrono",
 "cloud",
 "futures 0.3.28",
 "futures-util",
 "kvproto",
 "oauth2",
 "slog",
 "slog-global",
 "tikv_util",
 "tokio",
 "url",
]

[[package]]
name = "azure_core"
version = "0.1.0"
source = "git+https://github.com/Azure/azure-sdk-for-rust#b3c53f4cec4a6b541e49388b51e696dc892f18a3"
dependencies = [
 "async-trait",
 "base64 0.13.0",
 "bytes",
 "chrono",
 "dyn-clone",
 "futures 0.3.28",
 "getrandom 0.2.16",
 "http",
 "log",
 "oauth2",
 "rand 0.8.5",
 "reqwest",
 "rustc_version 0.4.0",
 "serde",
 "serde_derive",
 "serde_json",
 "thiserror",
 "url",
 "uuid 0.8.2",
]

[[package]]
name = "azure_identity"
version = "0.1.0"
source = "git+https://github.com/Azure/azure-sdk-for-rust#b3c53f4cec4a6b541e49388b51e696dc892f18a3"
dependencies = [
 "async-timer",
 "async-trait",
 "azure_core",
 "chrono",
 "futures 0.3.28",
 "log",
 "oauth2",
 "reqwest",
 "serde",
 "serde_json",
 "thiserror",
 "url",
]

[[package]]
name = "azure_storage"
version = "0.1.0"
source = "git+https://github.com/Azure/azure-sdk-for-rust#b3c53f4cec4a6b541e49388b51e696dc892f18a3"
dependencies = [
 "RustyXML",
 "async-trait",
 "azure_core",
 "base64 0.13.0",
 "bytes",
 "chrono",
 "futures 0.3.28",
 "http",
 "log",
 "md5",
 "once_cell",
 "ring 0.16.20",
 "serde",
 "serde-xml-rs",
 "serde_derive",
 "serde_json",
 "thiserror",
 "url",
 "uuid 0.8.2",
]

[[package]]
name = "backoff"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b62ddb9cb1ec0a098ad4bbf9344d0713fa193ae1a80af55febcff2627b6a00c1"
dependencies = [
 "getrandom 0.2.16",
 "instant",
 "rand 0.8.5",
]

[[package]]
name = "backtrace"
version = "0.3.61"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7a905d892734eea339e896738c14b9afce22b5318f64b951e70bf3844419b01"
dependencies = [
 "addr2line",
 "cc",
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "miniz_oxide 0.4.4",
 "object",
 "rustc-demangle",
]

[[package]]
name = "backup"
version = "0.0.1"
dependencies = [
 "api_version",
 "async-channel",
 "aws",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crc64fast",
 "encryption",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "external_storage",
 "external_storage_export",
 "file_system",
 "futures 0.3.28",
 "futures-util",
 "grpcio",
 "hex 0.4.3",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "pd_client",
 "prometheus",
 "raft",
 "raftstore",
 "rand 0.8.5",
 "security",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tempfile",
 "thiserror",
 "tidb_query_common",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "tokio-stream",
 "txn_types",
 "yatp",
]

[[package]]
name = "backup-stream"
version = "0.1.0"
dependencies = [
 "async-compression",
 "async-trait",
 "bytes",
 "chrono",
 "concurrency_manager",
 "crossbeam",
 "crossbeam-channel",
 "dashmap 5.1.0",
 "engine_panic",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "etcd-client",
 "external_storage",
 "external_storage_export",
 "fail 0.5.0",
 "file_system",
 "futures 0.3.28",
 "futures-io",
 "grpcio",
 "hex 0.4.3",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "openssl",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raftstore",
 "rand 0.8.5",
 "regex",
 "resolved_ts",
 "security",
 "slog",
 "slog-global",
 "tempdir",
 "tempfile",
 "test_raftstore",
 "test_util",
 "thiserror",
 "tidb_query_datatype",
 "tikv",
 "tikv_alloc",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tonic 0.8.2",
 "txn_types",
 "url",
 "uuid 0.8.2",
 "walkdir",
 "yatp",
]

[[package]]
name = "base64"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "904dfeac50f3cdaba28fc6f57fdcddb75f49ed61346676a78c4ffe55877802fd"

[[package]]
name = "base64"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ea22880d78093b0cbe17c89f64a7d457941e65759157ec6cb31a31d652b05e5"

[[package]]
name = "base64"
version = "0.21.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "604178f6c5c21f02dc555784810edfb88d34ac2c73b2eae109655649ee73ce3d"

[[package]]
name = "base64"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b3254f16251a8381aa12e40e3c4d2f0199f8c6508fbecb9d91f575e0fbb8c6"

[[package]]
name = "base64ct"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c3c1a368f70d6cf7302d78f8f7093da241fb8e8807c05cc9e51a125895a6d5b"

[[package]]
name = "batch-system"
version = "0.1.0"
dependencies = [
 "collections",
 "criterion",
 "crossbeam",
 "derive_more",
 "fail 0.5.0",
 "file_system",
 "lazy_static",
 "online_config",
 "prometheus",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "bcc"
version = "0.0.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5dbbe5cc2887bc0bc8506b26dcd4c41d1b54bdf4ff1de8e12d404deee60e4ec"
dependencies = [
 "bcc-sys",
 "bitflags 1.3.2",
 "byteorder",
 "libc 0.2.174",
 "regex",
 "thiserror",
]

[[package]]
name = "bcc-sys"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42d3c07869b846ba3306739375e9ed2f8055a8759fcf7f72ab7bf3bc4df38b9b"

[[package]]
name = "bigdecimal"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1e50562e37200edf7c6c43e54a08e64a5553bfb59d9c297d5572512aa517256"
dependencies = [
 "num-bigint 0.3.3",
 "num-integer",
 "num-traits",
]

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bindgen"
version = "0.59.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bd2a9a458e8f4304c52c43ebb0cfbd520289f8379a52e329a38afda99bf8eb8"
dependencies = [
 "bitflags 1.3.2",
 "cexpr",
 "clang-sys",
 "clap 2.34.0",
 "env_logger",
 "lazy_static",
 "lazycell",
 "log",
 "peeking_take_while",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash",
 "shlex 1.1.0",
 "which",
]

[[package]]
name = "bindgen"
version = "0.65.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfdf7b466f9a4903edc73f95d6d2bcd5baf8ae620638762244d3f60143643cc5"
dependencies = [
 "bitflags 1.3.2",
 "cexpr",
 "clang-sys",
 "lazy_static",
 "lazycell",
 "peeking_take_while",
 "prettyplease 0.2.12",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash",
 "shlex 1.1.0",
 "syn 2.0.104",
]

[[package]]
name = "bit-set"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0700ddab506f33b20a03b13996eccd309a48e5ff77d0d95926aa0210fb4e95f1"
dependencies = [
 "bit-vec",
]

[[package]]
name = "bit-vec"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "349f9b6a179ed607305526ca489b34ad0a41aed5f7980fa90eb03160b69598fb"

[[package]]
name = "bit_field"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dcb6dd1c2376d2e096796e234a70e17e94cc2d5d54ff8ce42b28cef1d0d359a4"

[[package]]
name = "bitfield"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46afbd2983a5d5a7bd740ccb198caf5b82f45c40c09c0eed36052d91cb92e719"

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf4b9d6a944f767f8e5e0db018570623c85f3d925ac718db4e06d0187adb21c1"

[[package]]
name = "bitpacking"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c1d3e2bfd8d06048a179f7b17afc3188effa10385e7b00dc65af6aae732ea92"
dependencies = [
 "crunchy",
]

[[package]]
name = "block-buffer"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4152116fd6e9dadb291ae18fc1ec3575ed6d84c29642d97890f4b4a3417297e4"
dependencies = [
 "generic-array",
]

[[package]]
name = "block-buffer"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3078c7629b62d3f0439517fa394996acacc5cbc91c5a20d8c658e77abd503a71"
dependencies = [
 "generic-array",
]

[[package]]
name = "boolinator"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfa8873f51c92e232f9bac4065cddef41b714152812bfc5f7672ba16d6ef8cd9"

[[package]]
name = "bstr"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba3569f383e8f1598449f1a423e72e99569137b47740b1da11ef19af3d5c3223"
dependencies = [
 "lazy_static",
 "memchr",
 "regex-automata 0.1.8",
]

[[package]]
name = "builtin_dfs"
version = "0.1.0"
dependencies = [
 "api_version",
 "async-trait",
 "bytes",
 "http",
 "hyper",
 "kvengine",
 "kvproto",
 "pd_client",
 "rand 0.8.5",
 "slog",
 "slog-global",
 "tikv-client",
 "tikv_util",
 "tokio",
]

[[package]]
name = "bumpalo"
version = "3.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d261e256854913907f67ed06efbc3338dfe6179796deefc1ff763fc1aee5535"

[[package]]
name = "bytemuck"
version = "1.14.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2ef034f05691a48569bd920a96c81b9d91bbad1ab5ac7c4616c1f6ef36cb79f"

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "bytes"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71b6127be86fdcfddb610f7182ac57211d4b18a3e9c82eb2d17662f2227ad6a"
dependencies = [
 "serde",
]

[[package]]
name = "bzip2-sys"
version = "0.1.11+1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "736a955f3fa7875102d57c82b8cac37ec45224a07fd32d58f9f7a186b6cd4cdc"
dependencies = [
 "cc",
 "libc 0.2.174",
 "pkg-config",
]

[[package]]
name = "c2-chacha"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d64d04786e0f528460fc884753cf8dddcc466be308f6026f8e355c41a0e4101"
dependencies = [
 "lazy_static",
 "ppv-lite86",
]

[[package]]
name = "cache-padded"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "631ae5198c9be5e753e5cc215e1bd73c2b466a3565173db433f52bb9d3e66dba"

[[package]]
name = "callgrind"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7f788eaf239475a3c1e1acf89951255a46c4b9b46cf3e866fc4d0707b4b9e36"
dependencies = [
 "libc 0.2.174",
 "valgrind_request",
]

[[package]]
name = "cargo_metadata"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46e3374c604fb39d1a2f35ed5e4a4e30e60d01fab49446e08f1b3e9a90aef202"
dependencies = [
 "semver 0.9.0",
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "case_macros"
version = "0.1.0"

[[package]]
name = "cast"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "926013f2860c46252efceabb19f4a6b308197505082c609025aa6706c011d427"

[[package]]
name = "cast"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37b2a672a2cb129a2e41c10b1224bb368f9f37a2b16b612598138befd7b37eb5"

[[package]]
name = "causal_ts"
version = "0.0.1"
dependencies = [
 "api_version",
 "async-trait",
 "criterion",
 "engine_rocks",
 "engine_traits",
 "enum_dispatch",
 "error_code",
 "fail 0.5.0",
 "futures 0.3.28",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "parking_lot 0.12.0",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "raft",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "test_pd_client",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "cc"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9e8aabfac534be767c909e0690571677d49f41bd8465ae876fe043d52ba5292"
dependencies = [
 "jobserver",
 "libc 0.2.174",
]

[[package]]
name = "cdc"
version = "0.0.1"
dependencies = [
 "api_version",
 "bitflags 1.3.2",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "criterion",
 "crossbeam",
 "engine_rocks",
 "engine_traits",
 "fail 0.5.0",
 "futures 0.3.28",
 "futures-timer",
 "getset",
 "grpcio",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "raftstore",
 "resolved_ts",
 "security",
 "semver 1.0.4",
 "slog",
 "slog-global",
 "tempfile",
 "test_pd_client",
 "test_raftstore",
 "test_util",
 "thiserror",
 "tikv",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "cedarwood"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d910bedd62c24733263d0bed247460853c9d22e8956bd4cd964302095e04e90"
dependencies = [
 "smallvec",
]

[[package]]
name = "census"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f4c707c6a209cbe82d10abd08e1ea8995e9ea937d2550646e02798948992be0"

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom 7.1.3",
]

[[package]]
name = "cfg-if"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4785bdd1c96b2a846b2bd7cc02e86b6b3dbf14e7e53446c4f54c92a361040822"

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "charabia"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b01abfd2db0eb8c4e7a47ccab5d1f67993736f4e76923ed9ae281c49070645d"
dependencies = [
 "aho-corasick",
 "csv",
 "either",
 "fst",
 "irg-kvariants",
 "jieba-rs",
 "once_cell",
 "serde",
 "slice-group-by",
 "unicode-normalization",
 "whatlang",
]

[[package]]
name = "chrono"
version = "0.4.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec837a71355b28f6556dbd569b37b3f363091c0bd4b2e735674521b4c5fd9bc5"
dependencies = [
 "android-tzdata",
 "iana-time-zone",
 "js-sys",
 "num-traits",
 "serde",
 "time 0.1.43",
 "wasm-bindgen",
 "winapi 0.3.9",
]

[[package]]
name = "chrono-tz"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2554a3155fec064362507487171dcc4edc3df60cb10f3a1fb10ed8094822b120"
dependencies = [
 "chrono",
 "parse-zoneinfo",
]

[[package]]
name = "clang-sys"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f54d78e30b388d4815220c8dd03fea5656b6c6d32adb59e89061552a102f8da1"
dependencies = [
 "glob",
 "libc 0.2.174",
 "libloading",
]

[[package]]
name = "clap"
version = "2.34.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0610544180c38b88101fecf2dd634b174a62eef6946f84dfc6a7127512b381c"
dependencies = [
 "ansi_term",
 "atty",
 "bitflags 1.3.2",
 "strsim 0.8.0",
 "textwrap 0.11.0",
 "unicode-width",
 "vec_map",
]

[[package]]
name = "clap"
version = "3.2.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86447ad904c7fb335a790c9d7fe3d0d971dc523b8ccd1561a520de9a85302750"
dependencies = [
 "atty",
 "bitflags 1.3.2",
 "clap_derive",
 "clap_lex",
 "indexmap 1.6.2",
 "once_cell",
 "strsim 0.10.0",
 "termcolor",
 "textwrap 0.15.1",
]

[[package]]
name = "clap_derive"
version = "3.2.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea0c8bce528c4be4da13ea6fead8965e95b6073585a2f05204bd8f4119f82a65"
dependencies = [
 "heck 0.4.0",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "clap_lex"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2850f2f5a82cbf437dd5af4d49848fbdfc27c157c3d010345776f952765261c5"
dependencies = [
 "os_str_bytes",
]

[[package]]
name = "clara_fts"
version = "0.0.1"
dependencies = [
 "anyhow",
 "charabia",
 "fastrand 2.3.0",
 "flate2",
 "lazy_static",
 "memmap2 0.9.5",
 "once_cell",
 "ordered-float 5.0.0",
 "paste",
 "prost 0.13.5",
 "prost-build 0.13.5",
 "serde_json",
 "stable_deref_trait",
 "tantivy",
 "tantivy-tokenizer-api",
 "tempfile",
]

[[package]]
name = "cloud"
version = "0.0.1"
dependencies = [
 "async-trait",
 "derive_more",
 "error_code",
 "fail 0.5.0",
 "futures-io",
 "kvproto",
 "lazy_static",
 "openssl",
 "prometheus",
 "protobuf",
 "rusoto_core",
 "thiserror",
 "tikv_util",
 "url",
]

[[package]]
name = "cloud_encryption"
version = "0.1.0"
dependencies = [
 "bytes",
 "derive_more",
 "hmac",
 "openssl",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "serde_json",
 "sha2 0.9.1",
]

[[package]]
name = "cloud_server"
version = "0.0.1"
dependencies = [
 "api_version",
 "async-stream 0.2.0",
 "backtrace",
 "backup",
 "builtin_dfs",
 "bytes",
 "cdc",
 "chrono",
 "clap 2.34.0",
 "cloud_encryption",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption",
 "encryption_export",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "fail 0.5.0",
 "file_system",
 "flate2",
 "fs2",
 "futures 0.3.28",
 "futures-timer",
 "grpcio",
 "grpcio-health",
 "hex 0.4.3",
 "hyper",
 "itertools 0.10.5",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "log",
 "log_wrappers",
 "mime",
 "nix 0.24.3",
 "nom 7.1.3",
 "num_cpus",
 "online_config",
 "openssl",
 "overload_protector",
 "paste",
 "pd_client",
 "pin-project",
 "pnet_datalink",
 "prometheus",
 "prometheus-static-metric",
 "promptly",
 "protobuf",
 "raft",
 "raft_log_engine",
 "raftstore",
 "rand 0.8.5",
 "recovery",
 "regex",
 "resolved_ts",
 "resource_metering",
 "rev_lines",
 "rfengine",
 "rfenginepb",
 "rfstore",
 "seahash",
 "security",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "signal-hook",
 "slog",
 "slog-global",
 "sst_importer",
 "sysinfo 0.26.9",
 "tempfile",
 "thiserror",
 "tidb_query_common",
 "tikv",
 "tikv_alloc",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "tokio-openssl",
 "tokio-timer",
 "tokio-util",
 "toml",
 "tonic 0.9.2",
 "tracker",
 "txn_types",
 "url",
 "uuid 0.8.2",
 "vlog",
 "walkdir",
 "yatp",
]

[[package]]
name = "cloud_worker"
version = "0.1.0"
dependencies = [
 "api_version",
 "async-trait",
 "bincode",
 "bytes",
 "chrono",
 "cloud_encryption",
 "cloud_server",
 "crc32c",
 "crc32fast",
 "dashmap 4.0.2",
 "file_system",
 "flate2",
 "fs2",
 "futures 0.3.28",
 "grpcio",
 "hex 0.4.3",
 "http",
 "hyper",
 "hyper-rustls",
 "k8s-openapi",
 "kube",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "load_data",
 "log_wrappers",
 "native_br",
 "pd_client",
 "prometheus",
 "protobuf",
 "quick_cache",
 "rand 0.8.5",
 "replication_worker",
 "rfenginepb",
 "rfstore",
 "schema",
 "security",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "slog",
 "slog-global",
 "slog-term",
 "tempfile",
 "test_raftstore",
 "test_util",
 "thiserror",
 "tidb_query_datatype",
 "tikv",
 "tikv-client",
 "tikv_util",
 "tokio",
 "tokio-util",
 "toml",
 "url",
]

[[package]]
name = "cmake"
version = "0.1.49"
source = "git+https://github.com/rust-lang/cmake-rs#07cbf8fd36168b8a5dc9e107f6e659130dbcdd30"
dependencies = [
 "cc",
]

[[package]]
name = "codec"
version = "0.0.1"
dependencies = [
 "byteorder",
 "bytes",
 "error_code",
 "libc 0.2.174",
 "panic_hook",
 "protobuf",
 "rand 0.8.5",
 "static_assertions",
 "thiserror",
 "tikv_alloc",
]

[[package]]
name = "codespan-reporting"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3538270d33cc669650c4b093848450d380def10c331d38c768e34cac80576e6e"
dependencies = [
 "termcolor",
 "unicode-width",
]

[[package]]
name = "collections"
version = "0.1.0"
dependencies = [
 "fxhash",
 "tikv_alloc",
]

[[package]]
name = "concurrency_manager"
version = "0.0.1"
dependencies = [
 "criterion",
 "crossbeam-skiplist",
 "fail 0.5.0",
 "futures 0.3.28",
 "kvproto",
 "parking_lot 0.12.0",
 "rand 0.8.5",
 "slog",
 "slog-global",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "concurrent-queue"
version = "1.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30ed07550be01594c6026cff2a1d7fe9c8f683caa798e12b68694ac9e88286a3"
dependencies = [
 "cache-padded",
]

[[package]]
name = "const-oid"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4c78c047431fee22c1a7bb92e00ad095a02a983affe4d8a72e2a2c62c1b94f3"

[[package]]
name = "coprocessor_plugin_api"
version = "0.1.0"
dependencies = [
 "async-trait",
 "atomic",
 "rustc_version 0.3.3",
]

[[package]]
name = "core-foundation"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a89e2ae426ea83155dccf10c0fa6b1463ef6d5fcb44cee0b224a408fa640a62"
dependencies = [
 "core-foundation-sys",
 "libc 0.2.174",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e496a50fda8aacccc86d7529e2c1e0892dbd0f898a6b5645b5561b89c3210efa"

[[package]]
name = "core2"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b49ba7ef1ad6107f8824dbe97de947cbaac53c44e7f9756a1fba0d37c1eec505"
dependencies = [
 "memchr",
]

[[package]]
name = "cpu-time"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9e393a7668fe1fad3075085b86c781883000b4ede868f43627b34a87c8b7ded"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "cpufeatures"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a17b76ff3a4162b0b27f354a0c87015ddad39d35f9c0c36607a3bdd175dde1f1"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "cpuid-bool"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8aebca1129a03dc6dc2b127edd729435bbc4a37e1d5f4d7513165089ceb02634"

[[package]]
name = "crc"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49fc9a695bca7f35f5f4c15cddc84415f66a74ea78eef08e90c5024f2b540e23"
dependencies = [
 "crc-catalog",
]

[[package]]
name = "crc-catalog"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccaeedb56da03b09f598226e25e80088cb4cd25f316e6e4df7d695f0feeb1403"

[[package]]
name = "crc32c"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8f48d60e5b4d2c53d5c2b1d8a58c849a70ae5e5509b08a48d047e3b65714a74"
dependencies = [
 "rustc_version 0.4.0",
]

[[package]]
name = "crc32fast"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b3855a8a784b474f333699ef2bbca9db2c4a1f6d9088a90a2d25b1eb53111eaa"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "crc64fast"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a82510de0a7cadd51dc68ff17da70aea0c80157f902230f9b157cecc2566318"

[[package]]
name = "criterion"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b01d6de93b2b6c65e17c634a26653a29d107b3c98c607c765bf38d041531cd8f"
dependencies = [
 "atty",
 "cast 0.3.0",
 "clap 2.34.0",
 "criterion-plot",
 "csv",
 "itertools 0.10.5",
 "lazy_static",
 "num-traits",
 "oorandom",
 "plotters",
 "rayon",
 "regex",
 "serde",
 "serde_cbor",
 "serde_derive",
 "serde_json",
 "tinytemplate",
 "walkdir",
]

[[package]]
name = "criterion-cpu-time"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63aaaf47e457badbcb376c65a49d0f182c317ebd97dc6d1ced94c8e1d09c0f3a"
dependencies = [
 "criterion",
 "libc 0.2.174",
]

[[package]]
name = "criterion-perf-events"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eba5111e09fabb08bfaedbe28c832876bb38d4f9519f715466332880d80b0eac"
dependencies = [
 "criterion",
 "perfcnt",
]

[[package]]
name = "criterion-plot"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d00996de9f2f7559f7f4dc286073197f83e92256a59ed395f9aac01fe717da57"
dependencies = [
 "cast 0.2.2",
 "itertools 0.10.5",
]

[[package]]
name = "crossbeam"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ae5588f6b3c3cb05239e90bd110f257254aecd01e4635400391aeae07497845"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-channel",
 "crossbeam-deque",
 "crossbeam-epoch 0.9.13",
 "crossbeam-queue",
 "crossbeam-utils 0.8.21",
]

[[package]]
name = "crossbeam-channel"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2dd04ddaf88237dc3b8d8f9a3c1004b506b54b3313403944054d23c0870c521"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.21",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.2"
source = "git+https://github.com/crossbeam-rs/crossbeam?rev=41ed3d948720f26149b2ebeaf58fe8a193134056#41ed3d948720f26149b2ebeaf58fe8a193134056"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-epoch 0.9.10",
 "crossbeam-utils 0.8.11",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.10"
source = "git+https://github.com/crossbeam-rs/crossbeam?rev=41ed3d948720f26149b2ebeaf58fe8a193134056#41ed3d948720f26149b2ebeaf58fe8a193134056"
dependencies = [
 "autocfg",
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.11",
 "memoffset 0.6.4",
 "once_cell",
 "scopeguard",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01a9af1f4c2ef74bb8aa1f7e19706bc72d03598c8a570bb5de72243c7a9d9d5a"
dependencies = [
 "autocfg",
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.21",
 "memoffset 0.7.1",
 "scopeguard",
]

[[package]]
name = "crossbeam-queue"
version = "0.3.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f58bbc28f91df819d0aa2a2c00cd19754769c2fad90579b3592b1c9ba7a3115"
dependencies = [
 "crossbeam-utils 0.8.21",
]

[[package]]
name = "crossbeam-skiplist"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "883a5821d7d079fcf34ac55f27a833ee61678110f6b97637cc74513c0d0b42fc"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-epoch 0.9.13",
 "crossbeam-utils 0.8.21",
 "scopeguard",
]

[[package]]
name = "crossbeam-utils"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3c7c73a2d1e9fc0886a08b93e98eb643461230d5f1925e4036204d5f2e261a8"
dependencies = [
 "autocfg",
 "cfg-if 0.1.10",
 "lazy_static",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.11"
source = "git+https://github.com/crossbeam-rs/crossbeam?rev=41ed3d948720f26149b2ebeaf58fe8a193134056#41ed3d948720f26149b2ebeaf58fe8a193134056"
dependencies = [
 "cfg-if 1.0.0",
 "once_cell",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0a5c400df2834b80a4c3327b3aad3a4c4cd4de0629063962b03235697506a28"

[[package]]
name = "crunchy"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a81dae078cea95a014a339291cec439d2f232ebe854a9d672b796c6afafa9b7"

[[package]]
name = "crypto-bigint"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03c6a1d5fa1de37e071642dfa44ec552ca5b299adb128fab16138e24b548fd21"
dependencies = [
 "generic-array",
 "subtle",
]

[[package]]
name = "crypto-common"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"
dependencies = [
 "generic-array",
 "typenum",
]

[[package]]
name = "crypto-mac"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4857fd85a0c34b3c3297875b747c1e02e06b6a0ea32dd892d8192b9ce0813ea6"
dependencies = [
 "generic-array",
 "subtle",
]

[[package]]
name = "cse-ctl"
version = "0.1.0"
dependencies = [
 "api_version",
 "async-trait",
 "bytes",
 "chrono",
 "clap 3.2.22",
 "cloud_encryption",
 "engine_traits",
 "farmhash",
 "futures 0.3.28",
 "hex 0.4.3",
 "http",
 "hyper",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "log_wrappers",
 "native_br",
 "pd_client",
 "protobuf",
 "raft-proto",
 "rfengine",
 "rfenginepb",
 "rfstore",
 "schema",
 "security",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "slog",
 "slog-global",
 "slog-term",
 "test_util",
 "tidb_query_datatype",
 "tikv",
 "tikv-client",
 "tikv_util",
 "time 0.1.43",
 "tokio",
 "toml",
 "txn_types",
 "url",
]

[[package]]
name = "csv"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acdc4883a9c96732e4733212c01447ebd805833b7275a73ca3ee080fd77afdaf"
dependencies = [
 "csv-core",
 "itoa 1.0.6",
 "ryu",
 "serde",
]

[[package]]
name = "csv-core"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d02f3b0da4c6504f86e9cd789d8dbafab48c2321be74e9987593de5a894d93d"
dependencies = [
 "memchr",
]

[[package]]
name = "cxx"
version = "1.0.129"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbdc8cca144dce1c4981b5c9ab748761619979e515c3d53b5df385c677d1d007"
dependencies = [
 "cc",
 "cxxbridge-flags",
 "cxxbridge-macro",
 "link-cplusplus",
]

[[package]]
name = "cxx-build"
version = "1.0.129"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5764c3142ab44fcf857101d12c0ddf09c34499900557c764f5ad0597159d1fc"
dependencies = [
 "cc",
 "codespan-reporting",
 "once_cell",
 "proc-macro2",
 "quote",
 "scratch",
 "syn 2.0.104",
]

[[package]]
name = "cxxbridge-flags"
version = "1.0.129"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d422aff542b4fa28c2ce8e5cc202d42dbf24702345c1fba3087b2d3f8a1b90ff"

[[package]]
name = "cxxbridge-macro"
version = "1.0.129"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1719100f31492cd6adeeab9a0f46cdbc846e615fdb66d7b398aa46ec7fdd06f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "darling"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fe629a532efad5526454efb0700f86d5ad7ff001acb37e431c8bf017a432a8e"
dependencies = [
 "darling_core 0.10.1",
 "darling_macro 0.10.1",
]

[[package]]
name = "darling"
version = "0.14.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b750cb3417fd1b327431a470f388520309479ab0bf5e323505daf0290cd3850"
dependencies = [
 "darling_core 0.14.4",
 "darling_macro 0.14.4",
]

[[package]]
name = "darling_core"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee54512bec54b41cf2337a22ddfadb53c7d4c738494dc2a186d7b037ad683b85"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim 0.9.2",
 "syn 1.0.107",
]

[[package]]
name = "darling_core"
version = "0.14.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "109c1ca6e6b7f82cc233a97004ea8ed7ca123a9af07a8230878fcfda9b158bf0"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim 0.10.0",
 "syn 1.0.107",
]

[[package]]
name = "darling_macro"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0cd3e432e52c0810b72898296a69d66b1d78d1517dff6cde7a130557a55a62c1"
dependencies = [
 "darling_core 0.10.1",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "darling_macro"
version = "0.14.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4aab4dbc9f7611d8b55048a3a16d2d010c2c8334e46304b40ac1cc14bf3b48e"
dependencies = [
 "darling_core 0.14.4",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "dary_heap"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04d2cd9c18b9f454ed67da600630b021a8a80bf33f8c95896ab33aaf1c26b728"

[[package]]
name = "dashmap"
version = "4.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e77a43b28d0668df09411cb0bc9a8c2adc40f9a048afe863e05fd43251e8e39c"
dependencies = [
 "cfg-if 1.0.0",
 "num_cpus",
]

[[package]]
name = "dashmap"
version = "5.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0834a35a3fce649144119e18da2a4d8ed12ef3862f47183fd46f625d072d96c"
dependencies = [
 "cfg-if 1.0.0",
 "num_cpus",
 "parking_lot 0.12.0",
]

[[package]]
name = "dashmap"
version = "6.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5041cc499144891f3790297212f32a74fb938e5136a14943f338ef9e0ae276cf"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.21",
 "hashbrown 0.14.5",
 "lock_api",
 "once_cell",
 "parking_lot_core 0.9.10",
]

[[package]]
name = "data-encoding"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e962a19be5cfc3f3bf6dd8f61eb50107f356ad6270fbb3ed41476571db78be5"

[[package]]
name = "debugid"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef552e6f588e446098f6ba40d89ac146c8c7b64aade83c051ee00bb5d2bc18d"
dependencies = [
 "uuid 1.2.2",
]

[[package]]
name = "der"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6919815d73839e7ad218de758883aae3a257ba6759ce7a9992501efbb53d705c"
dependencies = [
 "const-oid",
 "crypto-bigint",
 "pem-rfc7468",
]

[[package]]
name = "deranged"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c9e6a11ca8224451684bc0d7d5a7adbf8f2fd6887261a1cfc3c0432f9d4068e"
dependencies = [
 "powerfmt",
 "serde",
]

[[package]]
name = "derivative"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "derive-new"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3418329ca0ad70234b9735dc4ceed10af4df60eff9c8e7b06cb5e520d92c3535"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "derive_more"
version = "0.99.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a806e96c59a76a5ba6e18735b6cf833344671e61e7863f2edb5c518ea2cac95c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "digest"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3dd60d1080a57a05ab032377049e0591415d2b31afd7028356dbf3cc6dcb066"
dependencies = [
 "generic-array",
]

[[package]]
name = "digest"
version = "0.10.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed9a281f7bc9b7576e61468ba615a66a5c8cfdff42420a70aa82701a3b1e292"
dependencies = [
 "block-buffer 0.10.4",
 "crypto-common",
]

[[package]]
name = "dirs"
version = "4.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3aa72a6f96ea37bbc5aa912f6788242832f75369bdfdadcb0e38423f100059"
dependencies = [
 "dirs-sys",
]

[[package]]
name = "dirs-next"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf36e65a80337bea855cd4ef9b8401ffce06a7baedf2e85ec467b1ac3f6e82b6"
dependencies = [
 "cfg-if 1.0.0",
 "dirs-sys-next",
]

[[package]]
name = "dirs-next"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b98cf8ebf19c3d1b223e151f99a4f9f0690dca41414773390fc824184ac833e1"
dependencies = [
 "cfg-if 1.0.0",
 "dirs-sys-next",
]

[[package]]
name = "dirs-sys"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b1d1d91c932ef41c0f2663aa8b0ca0342d444d842c06914aa0a7e352d0bada6"
dependencies = [
 "libc 0.2.174",
 "redox_users",
 "winapi 0.3.9",
]

[[package]]
name = "dirs-sys-next"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"
dependencies = [
 "libc 0.2.174",
 "redox_users",
 "winapi 0.3.9",
]

[[package]]
name = "doc-comment"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "923dea538cea0aa3025e8685b20d6ee21ef99c4f77e954a30febbaac5ec73a97"

[[package]]
name = "dotenv"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77c90badedccf4105eca100756a0b1289e191f6fcbdadd3cee1d2f614f97da8f"

[[package]]
name = "downcast-rs"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b325c5dbd37f80359721ad39aca5a29fb04c89279657cffdda8736d0c0b9d2"

[[package]]
name = "dyn-clone"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee2626afccd7561a06cf1367e2950c4718ea04565e20fb5029b6c7d8ad09abcf"

[[package]]
name = "either"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c757948c5ede0e46177b7add2e67155f70e33c07fea8284df6576da70b3719"

[[package]]
name = "encoding_rs"
version = "0.8.29"
source = "git+https://github.com/xiongjiwei/encoding_rs.git?rev=68e0bc5a72a37a78228d80cd98047326559cf43c#68e0bc5a72a37a78228d80cd98047326559cf43c"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "encoding_rs"
version = "0.8.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75030f3c4f45dafd7586dd6780965a8c7e8e285a5ecb86713e63a79c5b2766f3"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "encryption"
version = "0.0.1"
dependencies = [
 "async-trait",
 "byteorder",
 "bytes",
 "crc32fast",
 "crossbeam",
 "derive_more",
 "engine_traits",
 "error_code",
 "fail 0.5.0",
 "file_system",
 "futures 0.3.28",
 "futures-util",
 "hex 0.4.3",
 "kvproto",
 "lazy_static",
 "matches",
 "online_config",
 "openssl",
 "prometheus",
 "protobuf",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tempfile",
 "test_util",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "toml",
]

[[package]]
name = "encryption_export"
version = "0.0.1"
dependencies = [
 "async-trait",
 "aws",
 "cloud",
 "derive_more",
 "encryption",
 "error_code",
 "file_system",
 "kvproto",
 "openssl",
 "protobuf",
 "rust-ini",
 "slog",
 "slog-global",
 "structopt",
 "tikv_util",
]

[[package]]
name = "engine_panic"
version = "0.0.1"
dependencies = [
 "engine_traits",
 "kvproto",
 "raft",
 "tikv_alloc",
 "tikv_util",
 "tracker",
 "txn_types",
]

[[package]]
name = "engine_rocks"
version = "0.0.1"
dependencies = [
 "api_version",
 "case_macros",
 "collections",
 "derive_more",
 "encryption",
 "engine_traits",
 "fail 0.5.0",
 "file_system",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "num_cpus",
 "online_config",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "rand 0.8.5",
 "regex",
 "rocksdb",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "slog_derive",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
 "time 0.1.43",
 "toml",
 "tracker",
 "txn_types",
]

[[package]]
name = "engine_rocks_helper"
version = "0.1.0"
dependencies = [
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "fail 0.5.0",
 "futures 0.3.28",
 "keys",
 "kvproto",
 "lazy_static",
 "pd_client",
 "prometheus",
 "protobuf",
 "raftstore",
 "slog",
 "slog-global",
 "tempfile",
 "tikv_util",
]

[[package]]
name = "engine_test"
version = "0.0.1"
dependencies = [
 "collections",
 "encryption",
 "engine_panic",
 "engine_rocks",
 "engine_traits",
 "file_system",
 "raft_log_engine",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "engine_traits"
version = "0.0.1"
dependencies = [
 "bytes",
 "case_macros",
 "error_code",
 "fail 0.5.0",
 "file_system",
 "kvproto",
 "log_wrappers",
 "protobuf",
 "raft",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "toml",
 "tracker",
 "txn_types",
]

[[package]]
name = "engine_traits_tests"
version = "0.0.1"
dependencies = [
 "engine_test",
 "engine_traits",
 "panic_hook",
 "tempfile",
 "tikv_alloc",
]

[[package]]
name = "enum_dispatch"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eb359f1476bf611266ac1f5355bc14aeca37b299d0ebccc038ee7058891c9cb"
dependencies = [
 "once_cell",
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "env_logger"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b2cf0344971ee6c64c31be0d530793fba457d322dfec2810c453d0ef228f9c3"
dependencies = [
 "atty",
 "humantime",
 "log",
 "regex",
 "termcolor",
]

[[package]]
name = "equator"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c35da53b5a021d2484a7cc49b2ac7f2d840f8236a286f84202369bd338d761ea"
dependencies = [
 "equator-macro",
]

[[package]]
name = "equator-macro"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bf679796c0322556351f287a51b49e48f7c4986e727b5dd78c972d30e2e16cc"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "equivalent"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5443807d6dff69373d433ab9ef5378ad8df50ca6298caf15de6e52e24aaf54d5"

[[package]]
name = "errno"
version = "0.3.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "778e2ac28f6c47af28e4907f13ffd1e1ddbd400980a9abd7c8df189bf578a5ad"
dependencies = [
 "libc 0.2.174",
 "windows-sys 0.52.0",
]

[[package]]
name = "error-chain"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ab49e9dcb602294bc42f9a7dfc9bc6e936fca4418ea300dbfb84fe16de0b7d9"
dependencies = [
 "backtrace",
 "version_check 0.1.5",
]

[[package]]
name = "error-code"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5115567ac25674e0043e472be13d14e537f37ea8aa4bdc4aef0c89add1db1ff"
dependencies = [
 "libc 0.2.174",
 "str-buf",
]

[[package]]
name = "error_code"
version = "0.0.1"
dependencies = [
 "grpcio",
 "kvproto",
 "lazy_static",
 "raft",
 "serde",
 "tikv_alloc",
]

[[package]]
name = "etcd-client"
version = "0.10.2"
source = "git+https://github.com/pingcap/etcd-client?rev=14a6f8731f1890d5fd2f6e16a9f0d0a306b0599e#14a6f8731f1890d5fd2f6e16a9f0d0a306b0599e"
dependencies = [
 "http",
 "hyper",
 "hyper-openssl",
 "openssl",
 "prost 0.11.9",
 "tokio",
 "tokio-stream",
 "tonic 0.8.2",
 "tonic-build",
 "tower",
 "tower-service",
 "visible",
]

[[package]]
name = "event-listener"
version = "2.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0206175f82b8d6bf6652ff7d71a1e27fd2e4efde587fd368662814d6ec1d9ce0"

[[package]]
name = "example_coprocessor_plugin"
version = "0.1.0"
dependencies = [
 "coprocessor_plugin_api",
]

[[package]]
name = "external_storage"
version = "0.0.1"
dependencies = [
 "async-compression",
 "async-trait",
 "bytes",
 "encryption",
 "engine_traits",
 "fail 0.5.0",
 "ffi-support",
 "file_system",
 "futures 0.3.28",
 "futures-executor",
 "futures-io",
 "futures-util",
 "grpcio",
 "kvproto",
 "lazy_static",
 "libloading",
 "matches",
 "openssl",
 "prometheus",
 "protobuf",
 "rand 0.8.5",
 "rusoto_core",
 "rust-ini",
 "slog",
 "slog-global",
 "structopt",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "tokio-util",
 "url",
]

[[package]]
name = "external_storage_export"
version = "0.0.1"
dependencies = [
 "async-compression",
 "async-trait",
 "aws",
 "azure",
 "cloud",
 "encryption",
 "engine_traits",
 "external_storage",
 "ffi-support",
 "file_system",
 "futures 0.3.28",
 "futures-executor",
 "futures-io",
 "futures-util",
 "gcp",
 "grpcio",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "libloading",
 "matches",
 "nix 0.24.3",
 "once_cell",
 "protobuf",
 "rust-ini",
 "signal-hook",
 "slog",
 "slog-global",
 "slog-term",
 "structopt",
 "tempfile",
 "tikv_util",
 "tokio",
 "tokio-util",
 "url",
]

[[package]]
name = "fail"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3be3c61c59fdc91f5dbc3ea31ee8623122ce80057058be560654c5d410d181a6"
dependencies = [
 "lazy_static",
 "log",
 "rand 0.7.3",
]

[[package]]
name = "fail"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec3245a0ca564e7f3c797d20d833a6870f57a728ac967d5225b3ffdef4465011"
dependencies = [
 "lazy_static",
 "log",
 "rand 0.8.5",
]

[[package]]
name = "fallible-iterator"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4443176a9f2c162692bd3d352d745ef9413eec5782a80d8fd6f8a1ac692a07f7"

[[package]]
name = "farmhash"
version = "1.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f35ce9c8fb9891c75ceadbc330752951a4e369b50af10775955aeb9af3eee34b"

[[package]]
name = "fastbloom"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18c1ddb9231d8554c2d6bdf4cfaabf0c59251658c68b6c95cd52dd0c513a912a"
dependencies = [
 "getrandom 0.3.3",
 "libm",
 "rand 0.9.2",
 "serde",
 "siphasher 1.0.1",
]

[[package]]
name = "fastdivide"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9afc2bd4d5a73106dd53d10d73d3401c2f32730ba2c0b93ddb888a8983680471"

[[package]]
name = "fastrand"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7a407cfaa3385c4ae6b23e84623d48c2798d06e3e6a1878f7f59f17b3f86499"
dependencies = [
 "instant",
]

[[package]]
name = "fastrand"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37909eebbb50d72f9059c3b6d82c0463f2ff062c9e95845c43a6c9c0355411be"

[[package]]
name = "ffi-support"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f85d4d1be103c0b2d86968f0b0690dc09ac0ba205b90adb0389b552869e5000e"
dependencies = [
 "lazy_static",
 "log",
]

[[package]]
name = "file_system"
version = "0.1.0"
dependencies = [
 "bcc",
 "collections",
 "crc32fast",
 "crossbeam-utils 0.8.21",
 "fs2",
 "lazy_static",
 "libc 0.2.174",
 "maligned",
 "online_config",
 "openssl",
 "parking_lot 0.12.0",
 "prometheus",
 "prometheus-static-metric",
 "rand 0.8.5",
 "serde",
 "slog",
 "slog-global",
 "strum 0.20.0",
 "tempfile",
 "thread_local",
 "tikv_alloc",
 "tikv_util",
 "tokio",
]

[[package]]
name = "filedescriptor"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed3d8a5e20435ff00469e51a0d82049bae66504b5c429920dadf9bb54d47b3f"
dependencies = [
 "libc 0.2.174",
 "thiserror",
 "winapi 0.3.9",
]

[[package]]
name = "filetime"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d34cfa13a63ae058bfa601fe9e313bbdb3746427c1459185464ce0fcf62e1e8"
dependencies = [
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "redox_syscall 0.2.11",
 "winapi 0.3.9",
]

[[package]]
name = "findshlibs"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d691fdb3f817632d259d09220d4cf0991dbb2c9e59e044a02a59194bf6e14484"
dependencies = [
 "cc",
 "lazy_static",
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "finl_unicode"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fcfdc7a0362c9f4444381a9e697c79d435fe65b52a37466fc2c1184cee9edc6"

[[package]]
name = "fixedbitset"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce7134b9999ecaf8bcd65542e436736ef32ddca1b3e06094cb6ec5755203b80"

[[package]]
name = "flatbuffers"
version = "2.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b428b715fdbdd1c364b84573b5fdc0f84f8e423661b9f398735278bc7f2b6a"
dependencies = [
 "bitflags 1.3.2",
 "smallvec",
 "thiserror",
]

[[package]]
name = "flate2"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a3d7db9596fecd151c5f638c0ee5d5bd487b6e0ea232e5dc96d5250f6f94b1d"
dependencies = [
 "crc32fast",
 "libz-sys",
 "miniz_oxide 0.8.9",
]

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "foldhash"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9c4f5dac5e15c24eb999c26181a6ca40b39fe946cbe4c263c7209467bc83af2"

[[package]]
name = "foreign-types"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6f339eb8adc052cd2ca78910fda869aefa38d22d5cb648e6485e4d3fc06f3b1"
dependencies = [
 "foreign-types-shared",
]

[[package]]
name = "foreign-types-shared"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0228411908ca8685dba7fc2cdd70ec9990a6e753e89b6ac91a84c40fbaf4b"

[[package]]
name = "form_urlencoded"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5fc25a87fa4fd2094bffb06925852034d90a17f0d1e05197d4956d3555752191"
dependencies = [
 "matches",
 "percent-encoding",
]

[[package]]
name = "fs2"
version = "0.4.3"
source = "git+https://github.com/tikv/fs2-rs?branch=tikv#cd503764a19a99d74c1ab424dd13d6bcd093fcae"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "fs4"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7e180ac76c23b45e767bd7ae9579bc0bb458618c4bc71835926e098e61d15f8"
dependencies = [
 "rustix",
 "windows-sys 0.52.0",
]

[[package]]
name = "fs_extra"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42703706b716c37f96a77aea830392ad231f44c9e9a67872fa5548707e11b11c"

[[package]]
name = "fsevent"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ab7d1bd1bd33cc98b0889831b72da23c0aa4df9cec7e0702f46ecea04b35db6"
dependencies = [
 "bitflags 1.3.2",
 "fsevent-sys",
]

[[package]]
name = "fsevent-sys"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f41b048a94555da0f42f1d632e2e19510084fb8e303b0daa2816e733fb3644a0"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "fslock"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57eafdd0c16f57161105ae1b98a1238f97645f2f588438b2949c99a2af9616bf"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "fst"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ab85b9b05e3978cc9a9cf8fea7f01b494e1a09ed3037e16ba39edc7a29eb61a"

[[package]]
name = "fuchsia-cprng"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a06f77d526c1a601b7c4cdd98f54b5eaabffc14d5f2f0296febdc7f357c6d3ba"

[[package]]
name = "fuchsia-zircon"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e9763c69ebaae630ba35f74888db465e49e259ba1bc0eda7d06f4a067615d82"
dependencies = [
 "bitflags 1.3.2",
 "fuchsia-zircon-sys",
]

[[package]]
name = "fuchsia-zircon-sys"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3dcaa9ae7725d12cdb85b3ad99a434db70b468c09ded17e012d86b5c1010f7a7"

[[package]]
name = "futures"
version = "0.1.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a471a38ef8ed83cd6e40aa59c1ffe17db6855c18e3604d9c4ed8c08ebc28678"

[[package]]
name = "futures"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23342abe12aba583913b2e62f22225ff9c950774065e4bfb61a19cd9770fec40"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-channel"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "955518d47e09b25bbebc7a18df10b81f0c766eaf4c4f1cccef2fca5f2a4fb5f2"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4bca583b7e26f571124fe5b7561d49cb2868d79116cfa0eefce955557c6fee8c"

[[package]]
name = "futures-executor"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccecee823288125bd88b4d7f565c9e58e41858e47ab72e8ea2d64e93624386e0"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
 "num_cpus",
]

[[package]]
name = "futures-intrusive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a604f7a68fbf8103337523b1fadc8ade7361ee3f112f7c680ad179651616aed5"
dependencies = [
 "futures-core",
 "lock_api",
 "parking_lot 0.11.1",
]

[[package]]
name = "futures-io"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fff74096e71ed47f8e023204cfd0aa1289cd54ae5430a9523be060cdb849964"

[[package]]
name = "futures-macro"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89ca545a94061b6365f2c7355b4b32bd20df3ff95f02da9329b34ccc3bd6ee72"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "futures-sink"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f43be4fe21a13b9781a69afa4985b0f6ee0e1afab2c6f454a8cf30e2b2237b6e"

[[package]]
name = "futures-task"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76d3d132be6c0e6aa1534069c705a74a5997a356c0dc2f86a47765e5617c5b65"

[[package]]
name = "futures-timer"
version = "3.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e64b03909df88034c26dc1547e8970b91f98bdb65165d6a4e9110d94263dbb2c"

[[package]]
name = "futures-util"
version = "0.3.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26b01e40b772d54cf6c6d721c1d1abd0647a0106a12ecaa1c186273392a69533"
dependencies = [
 "futures 0.1.31",
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "slab",
]

[[package]]
name = "fuzz"
version = "0.0.1"
dependencies = [
 "anyhow",
 "cargo_metadata",
 "lazy_static",
 "regex",
 "structopt",
]

[[package]]
name = "fuzz-targets"
version = "0.0.1"
dependencies = [
 "anyhow",
 "byteorder",
 "tidb_query_datatype",
 "tikv_util",
]

[[package]]
name = "fuzzer-afl"
version = "0.0.1"
dependencies = [
 "afl",
 "fuzz-targets",
]

[[package]]
name = "fuzzer-honggfuzz"
version = "0.0.1"
dependencies = [
 "fuzz-targets",
 "honggfuzz",
]

[[package]]
name = "fuzzer-libfuzzer"
version = "0.0.1"
dependencies = [
 "fuzz-targets",
 "libfuzzer-sys",
]

[[package]]
name = "fxhash"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c31b6d751ae2c7f11320402d34e41349dd1016f8d5d45e48c4312bc8625af50c"
dependencies = [
 "byteorder",
]

[[package]]
name = "gag"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a713bee13966e9fbffdf7193af71d54a6b35a0bb34997cd6c9519ebeb5005972"
dependencies = [
 "filedescriptor",
 "tempfile",
]

[[package]]
name = "gcc"
version = "0.3.55"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f5f3913fa0bfe7ee1fd8248b6b9f42a5af4b9d65ec2dd2c3c26132b950ecfc2"

[[package]]
name = "gcp"
version = "0.0.1"
dependencies = [
 "async-trait",
 "cloud",
 "futures-util",
 "http",
 "hyper",
 "hyper-tls",
 "kvproto",
 "matches",
 "pin-project",
 "slog",
 "slog-global",
 "tame-gcs",
 "tame-oauth",
 "tikv_util",
 "tokio",
 "url",
]

[[package]]
name = "generic-array"
version = "0.14.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "501466ecc8a30d1d3b7fc9229b122b2ce8ed6e9d9223f1138d4babb253e51817"
dependencies = [
 "typenum",
 "version_check 0.9.4",
]

[[package]]
name = "getrandom"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "473a1265acc8ff1e808cd0a1af8cee3c2ee5200916058a2ca113c29f2d903571"
dependencies = [
 "cfg-if 0.1.10",
 "libc 0.2.174",
 "wasi 0.7.0",
]

[[package]]
name = "getrandom"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "335ff9f135e4384c8150d6f27c6daed433577f86b4750418338c01a1a2528592"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "libc 0.2.174",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "wasm-bindgen",
]

[[package]]
name = "getrandom"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26145e563e54f2cadc477553f1ec5ee650b00862f0a58bcd12cbdc5f0ea2d2f4"
dependencies = [
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "r-efi",
 "wasi 0.14.2+wasi-0.2.4",
]

[[package]]
name = "getset"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24b328c01a4d71d2d8173daa93562a73ab0fe85616876f02500f53d82948c504"
dependencies = [
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "gimli"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0a01e0497841a3b2db4f8afa483cce65f7e96a3498bd6c541734792aeac8fe7"

[[package]]
name = "glob"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8d1add55171497b4705a648c6b583acafb01d58050a51727785f0b2c8e0a2b2"

[[package]]
name = "gperftools"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20a3fc5818b1223ec628fc6998c8900486208b577f78c07500d4b52f983ebc9d"
dependencies = [
 "error-chain",
 "lazy_static",
 "pkg-config",
]

[[package]]
name = "grpcio"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f2506de56197d01821c2d1d21082d2dcfd6c82d7a1d6e04d33f37aab6130632"
dependencies = [
 "futures-executor",
 "futures-util",
 "grpcio-sys",
 "libc 0.2.174",
 "log",
 "parking_lot 0.11.1",
 "protobuf",
]

[[package]]
name = "grpcio-compiler"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed97a17310fd00ff4109357584a00244e2a785d05b7ee0ef4d1e8fb1d84266df"
dependencies = [
 "protobuf",
]

[[package]]
name = "grpcio-health"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a37eae605cd21f144b7c7fd0e64e57af9f73d132756fef5b706db110c3ec7ea0"
dependencies = [
 "futures-executor",
 "futures-util",
 "grpcio",
 "log",
 "protobuf",
]

[[package]]
name = "grpcio-sys"
version = "0.10.3+1.44.0-patched"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f23adc509a3c4dea990e0ab8d2add4a65389ee69c288b7851d75dd1df7a6d6c6"
dependencies = [
 "bindgen 0.59.2",
 "cc",
 "cmake",
 "libc 0.2.174",
 "libz-sys",
 "openssl-sys",
 "pkg-config",
 "walkdir",
]

[[package]]
name = "h2"
version = "0.3.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81fe527a889e1532da5c525686d96d4c2e74cdd345badf8dfef9f6b39dd5f5e8"
dependencies = [
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "futures-util",
 "http",
 "indexmap 2.10.0",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "half"
version = "1.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eabb4a44450da02c90444cf74558da904edde8fb4e9035a9a6a4e15445af0bd7"

[[package]]
name = "half"
version = "2.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6dd08c532ae367adf81c312a4580bc67f1d0fe8bc9c460520283f4c0ff277888"
dependencies = [
 "cfg-if 1.0.0",
 "crunchy",
]

[[package]]
name = "hashbrown"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d7afe4a420e3fe79967a00898cc1f4db7c8a49a9333a29f8a4bd76a253d5cd04"

[[package]]
name = "hashbrown"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab5ef0d4909ef3724cc8cce6ccc8572c5c817592e9285f5464f8e86f8bd3726e"
dependencies = [
 "ahash 0.7.8",
]

[[package]]
name = "hashbrown"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c21d40587b92fa6a6c6e3c1bdbf87d75511db5672f9c93175574b3a00df1758"
dependencies = [
 "ahash 0.7.8",
]

[[package]]
name = "hashbrown"
version = "0.14.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1"
dependencies = [
 "ahash 0.8.11",
 "allocator-api2",
]

[[package]]
name = "hashbrown"
version = "0.15.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5971ac85611da7067dbfcabef3c70ebb5606018acd9e2a3903a0da507521e0d5"
dependencies = [
 "allocator-api2",
 "equivalent",
 "foldhash",
]

[[package]]
name = "hashlink"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7249a3129cbc1ffccd74857f81464a323a152173cdb134e0fd81bc803b29facf"
dependencies = [
 "hashbrown 0.11.2",
]

[[package]]
name = "heck"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20564e78d53d2bb135c343b3f47714a56af2061f1c928fdb541dc7b9fdd94205"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "heck"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2540771e65fc8cb83cd6e8a237f70c319bd5c29f78ed1084ba5d50eeac86f7f9"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "hermit-abi"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "307c3c9f937f38e3534b1d6447ecf090cafcc9744e4a6360e8b037b2cf5af120"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "hermit-abi"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbf6a919d6cf397374f7dfeeea91d974c7c0a7221d0d0f4f20d859d329e53fcc"

[[package]]
name = "hex"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "805026a5d0141ffc30abb3be3173848ad46a1b1664fe632428479619a3644d77"

[[package]]
name = "hex"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"

[[package]]
name = "hexhex"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c56592988a798d4df9155ea41f1d83d1e9a3eefd50fb0c31eb861b6c273ed2fc"
dependencies = [
 "hexhex_impl",
 "hexhex_macros",
]

[[package]]
name = "hexhex_impl"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f530c66081961311028f67d22da8e3f739dab6979fc200f199c3ef0fb2e194a"
dependencies = [
 "fallible-iterator",
]

[[package]]
name = "hexhex_macros"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd0ae2c93351a14664ef0fa185c434a56783fb83a71b158a3389d594dd391e1f"
dependencies = [
 "hexhex_impl",
]

[[package]]
name = "hmac"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1441c6b1e930e2817404b5046f1f989899143a12bf92de603b69f4e0aee1e15"
dependencies = [
 "crypto-mac",
 "digest 0.9.0",
]

[[package]]
name = "honggfuzz"
version = "0.5.55"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "848e9c511092e0daa0a35a63e8e6e475a3e8f870741448b9f6028d69b142f18e"
dependencies = [
 "arbitrary 1.2.3",
 "lazy_static",
 "memmap2 0.5.3",
 "rustc_version 0.4.0",
]

[[package]]
name = "htmlescape"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9025058dae765dee5070ec375f591e2ba14638c63feff74f13805a72e523163"

[[package]]
name = "http"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd6effc99afb63425aff9b05836f029929e345a6148a14b7ecd5ab67af944482"
dependencies = [
 "bytes",
 "fnv",
 "itoa 1.0.6",
]

[[package]]
name = "http-body"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d5f38f16d184e36f2408a55281cd658ecbd3ca05cce6d6510a176eca393e26d1"
dependencies = [
 "bytes",
 "http",
 "pin-project-lite",
]

[[package]]
name = "http-range-header"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bfe8eed0a9285ef776bb792479ea3834e8b94e13d615c2f66d03dd50a435a29"

[[package]]
name = "httparse"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6dbf3de79e51f3d586ab4cb9d5c3e2c14aa28ed23d180cf89b4df0454a69cc87"

[[package]]
name = "httpdate"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05842d0d43232b23ccb7060ecb0f0626922c21f30012e97b767b30afd4a5d4b9"

[[package]]
name = "humantime"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a3a5bfb195931eeb336b2a7b4d761daec841b97f947d34394601737a7bba5e4"

[[package]]
name = "hyper"
version = "0.14.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab302d72a6f11a3b910431ff93aae7e773078c769f0a3ef15fb9ec692ed147d4"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "httparse",
 "httpdate",
 "itoa 1.0.6",
 "pin-project-lite",
 "socket2",
 "tokio",
 "tower-service",
 "tracing",
 "want",
]

[[package]]
name = "hyper-openssl"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6ee5d7a8f718585d1c3c61dfde28ef5b0bb14734b4db13f5ada856cdc6c612b"
dependencies = [
 "http",
 "hyper",
 "linked_hash_set",
 "once_cell",
 "openssl",
 "openssl-sys",
 "parking_lot 0.12.0",
 "tokio",
 "tokio-openssl",
 "tower-layer",
]

[[package]]
name = "hyper-rustls"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec3efd23720e2049821a693cbc7e65ea87c72f1c58ff2f9522ff332b1491e590"
dependencies = [
 "futures-util",
 "http",
 "hyper",
 "log",
 "rustls 0.21.11",
 "rustls-native-certs",
 "tokio",
 "tokio-rustls 0.24.1",
]

[[package]]
name = "hyper-timeout"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbb958482e8c7be4bc3cf272a766a2b0bf1a6755e7a6ae777f017a31d11b13b1"
dependencies = [
 "hyper",
 "pin-project-lite",
 "tokio",
 "tokio-io-timeout",
]

[[package]]
name = "hyper-tls"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6183ddfa99b85da61a140bea0efc93fdf56ceaa041b37d553518030827f9905"
dependencies = [
 "bytes",
 "hyper",
 "native-tls",
 "tokio",
 "tokio-native-tls",
]

[[package]]
name = "iana-time-zone"
version = "0.1.57"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fad5b825842d2b38bd206f3e81d6957625fd7f0a361e345c30e01a0ae2dd613"
dependencies = [
 "android_system_properties",
 "core-foundation-sys",
 "iana-time-zone-haiku",
 "js-sys",
 "wasm-bindgen",
 "windows",
]

[[package]]
name = "iana-time-zone-haiku"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f31827a206f56af32e590ba56d5d2d085f558508192593743f16b2306495269f"
dependencies = [
 "cc",
]

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02e2673c30ee86b5b96a9cb52ad15718aa1f966f5ab9ad54a8b95d5ca33120a9"
dependencies = [
 "matches",
 "unicode-bidi",
 "unicode-normalization",
]

[[package]]
name = "if_chain"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb56e1aa765b4b4f3aadfab769793b7087bb03a4ea4920644a6d238e2df5b9ed"

[[package]]
name = "include-flate"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df49c16750695486c1f34de05da5b7438096156466e7f76c38fcdf285cf0113e"
dependencies = [
 "include-flate-codegen",
 "lazy_static",
 "libflate",
]

[[package]]
name = "include-flate-codegen"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c5b246c6261be723b85c61ecf87804e8ea4a35cb68be0ff282ed84b95ffe7d7"
dependencies = [
 "libflate",
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "indexmap"
version = "1.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "824845a0bf897a9042383849b02c1bc219c2383772efcd5c6f9766fa4b81aef3"
dependencies = [
 "autocfg",
 "hashbrown 0.9.1",
]

[[package]]
name = "indexmap"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe4cd85333e22411419a0bcae1297d25e58c9443848b11dc6a86fefe8c78a661"
dependencies = [
 "equivalent",
 "hashbrown 0.15.4",
]

[[package]]
name = "inferno"
version = "0.11.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "232929e1d75fe899576a3d5c7416ad0d88dbfbb3c3d6aa00873a7408a50ddb88"
dependencies = [
 "ahash 0.8.11",
 "indexmap 2.10.0",
 "is-terminal",
 "itoa 1.0.6",
 "log",
 "num-format",
 "once_cell",
 "quick-xml 0.26.0",
 "rgb",
 "str_stack",
]

[[package]]
name = "inotify"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4816c66d2c8ae673df83366c18341538f234a26d65a9ecea5c348b453ac1d02f"
dependencies = [
 "bitflags 1.3.2",
 "inotify-sys",
 "libc 0.2.174",
]

[[package]]
name = "inotify-sys"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e05c02b5e89bff3b946cedeca278abc628fe811e604f027c45a8aa3cf793d0eb"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "instant"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a5bbe824c507c5da5956355e86a746d82e0e1464f65d862cc5e71da70e94b2c"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "into_other"
version = "0.0.1"
dependencies = [
 "engine_traits",
 "kvproto",
 "raft",
]

[[package]]
name = "iovec"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2b3ea6ff95e175473f8ffe6a7eb7c00d054240321b84c57051175fe3c1e075e"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "ipnet"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47be2f14c678be2fdcab04ab1171db51b2762ce6f0a8ee87c8dd4a04ed216135"

[[package]]
name = "ipnetwork"
version = "0.15.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a69dd5e3613374e74da81c251750153abe3bd0ad17641ea63d43d1e21d0dbd4d"
dependencies = [
 "serde",
]

[[package]]
name = "irg-kvariants"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef2af7c331f2536964a32b78a7d2e0963d78b42f4a76323b16cc7d94b1ddce26"
dependencies = [
 "csv",
 "once_cell",
 "serde",
]

[[package]]
name = "is-terminal"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "261f68e344040fbd0edea105bef17c66edf46f984ddb1115b775ce31be948f4b"
dependencies = [
 "hermit-abi 0.4.0",
 "libc 0.2.174",
 "windows-sys 0.52.0",
]

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1c173a5686ce8bfa551b3563d0c2170bf24ca44da99c7ca4bfdab5418c3fe57"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba291022dbbd398a455acf126c1e341954079855bc60dfdda641363bd6922569"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b192c782037fadd9cfa75548310488aabdbf3d2da73885b31bd0abd03351285"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b71991ff56294aa922b450139ee08b3bfc70982c6b2c7562771375cf73542dd4"

[[package]]
name = "itoa"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "453ad9f582a441959e5f0d088b02ce04cfe8d51a8eaf077f12ac6d3e94164ca6"

[[package]]
name = "jieba-macros"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c676b32a471d3cfae8dac2ad2f8334cd52e53377733cca8c1fb0a5062fec192"
dependencies = [
 "phf_codegen 0.11.3",
]

[[package]]
name = "jieba-rs"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b06096b4b61fb4bfdbf16c6a968ea2d6be1ac9617cf3db741c3b641e6c290a35"
dependencies = [
 "cedarwood",
 "fxhash",
 "include-flate",
 "jieba-macros",
 "lazy_static",
 "phf 0.11.3",
 "regex",
]

[[package]]
name = "jobserver"
version = "0.1.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48d1dbcbbeb6a7fec7e059840aa538bd62aaccf972c7346c4d9d2059312853d0"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "js-sys"
version = "0.3.56"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a38fc24e30fd564ce974c02bf1d337caddff65be6cc4735a1f7eab22a7440f04"
dependencies = [
 "wasm-bindgen",
]

[[package]]
name = "json-patch"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55ff1e1486799e3f64129f8ccad108b38290df9cd7015cd31bed17239f0789d6"
dependencies = [
 "serde",
 "serde_json",
 "thiserror",
 "treediff",
]

[[package]]
name = "jsonpath_lib"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaa63191d68230cccb81c5aa23abd53ed64d83337cacbb25a7b8c7979523774f"
dependencies = [
 "log",
 "serde",
 "serde_json",
]

[[package]]
name = "k8s-openapi"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd990069640f9db34b3b0f7a1afc62a05ffaa3be9b66aa3c313f58346df7f788"
dependencies = [
 "base64 0.21.2",
 "bytes",
 "chrono",
 "http",
 "percent-encoding",
 "serde",
 "serde-value",
 "serde_json",
 "url",
]

[[package]]
name = "kernel32-sys"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7507624b29483431c0ba2d82aece8ca6cdba9382bff4ddd0f7490560c056098d"
dependencies = [
 "winapi 0.2.8",
 "winapi-build",
]

[[package]]
name = "keyed_priority_queue"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d63b6407b66fc81fc539dccf3ddecb669f393c5101b6a2be3976c95099a06e8"
dependencies = [
 "indexmap 1.6.2",
]

[[package]]
name = "keys"
version = "0.1.0"
dependencies = [
 "byteorder",
 "kvproto",
 "log_wrappers",
 "panic_hook",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "kube"
version = "0.84.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14bd236a6f6ddeac3fefa2863eb4e363cb3a2c49d66619e181b5b8f8f0787575"
dependencies = [
 "k8s-openapi",
 "kube-client",
 "kube-core",
 "kube-derive",
 "kube-runtime",
]

[[package]]
name = "kube-client"
version = "0.84.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04a28620131ca89b2509e52f5e1b71bfa3e61a50321836b2ae373bc18e0309e6"
dependencies = [
 "base64 0.20.0",
 "bytes",
 "chrono",
 "dirs-next 2.0.0",
 "either",
 "futures 0.3.28",
 "http",
 "http-body",
 "hyper",
 "hyper-openssl",
 "hyper-timeout",
 "jsonpath_lib",
 "k8s-openapi",
 "kube-core",
 "openssl",
 "pem",
 "pin-project",
 "rand 0.8.5",
 "secrecy",
 "serde",
 "serde_json",
 "serde_yaml",
 "thiserror",
 "tokio",
 "tokio-tungstenite",
 "tokio-util",
 "tower",
 "tower-http 0.4.0",
 "tracing",
]

[[package]]
name = "kube-core"
version = "0.84.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8227a989f1eeee3bcbf045165d6aca462af3744ecd4dfdcfba81051fb7de428e"
dependencies = [
 "chrono",
 "form_urlencoded",
 "http",
 "json-patch",
 "k8s-openapi",
 "once_cell",
 "schemars",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "kube-derive"
version = "0.84.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19d227fcf3e12f53ea1a38d4766a8c29f8b27795579e4146464effb88d52dd99"
dependencies = [
 "darling 0.14.4",
 "proc-macro2",
 "quote",
 "serde_json",
 "syn 1.0.107",
]

[[package]]
name = "kube-runtime"
version = "0.84.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6834a4a1f53a8528d5f346cdd141a77dbda31beb33dab4bf24fa4ecf6c508744"
dependencies = [
 "ahash 0.8.11",
 "async-trait",
 "backoff",
 "derivative",
 "futures 0.3.28",
 "json-patch",
 "k8s-openapi",
 "kube-client",
 "parking_lot 0.12.0",
 "pin-project",
 "serde",
 "serde_json",
 "smallvec",
 "thiserror",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "kvengine"
version = "0.0.1"
dependencies = [
 "aligned-vec",
 "aliyun",
 "anyhow",
 "api_version",
 "arrow-buffer",
 "async-recursion 1.1.1",
 "async-trait",
 "aws",
 "backtrace",
 "base64 0.13.0",
 "bincode",
 "bstr",
 "bytemuck",
 "byteorder",
 "bytes",
 "clara_fts",
 "cloud_encryption",
 "codec",
 "collections",
 "crc32c",
 "crc32fast",
 "criterion",
 "crossbeam",
 "crossbeam-queue",
 "dashmap 4.0.2",
 "dyn-clone",
 "engine_panic",
 "engine_rocks",
 "engine_traits",
 "fail 0.5.0",
 "farmhash",
 "fastbloom",
 "file_system",
 "filetime",
 "fslock",
 "futures 0.3.28",
 "hex 0.4.3",
 "hexhex",
 "http",
 "hyper",
 "hyper-rustls",
 "hyper-tls",
 "itertools 0.10.5",
 "keys",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "log_wrappers",
 "lz4",
 "maybe-async",
 "memmap2 0.9.5",
 "nix 0.24.3",
 "num_cpus",
 "once_cell",
 "papaya",
 "parking_lot 0.12.0",
 "prometheus",
 "prometheus-static-metric",
 "proptest",
 "protobuf",
 "quick-xml 0.23.1",
 "quick_cache",
 "rand 0.8.5",
 "recovery",
 "regex",
 "rocksdb",
 "rstest",
 "rusoto_core",
 "rusoto_credential",
 "rusoto_mock",
 "rusoto_s3",
 "schema",
 "security",
 "serde",
 "serde_derive",
 "serde_json",
 "slog",
 "slog-global",
 "slog-term",
 "tempfile",
 "test_util",
 "thiserror",
 "tidb_query_common",
 "tidb_query_datatype",
 "tikv_alloc",
 "tikv_util",
 "time 0.1.43",
 "tipb",
 "tokio",
 "tokio-util",
 "tracker",
 "txn_types",
 "url",
 "usearch",
 "xorf",
 "zipf 7.0.0",
 "zstd-sys",
]

[[package]]
name = "kvenginepb"
version = "0.0.1"
dependencies = [
 "protobuf",
 "protobuf-codegen-pure",
]

[[package]]
name = "kvproto"
version = "0.0.2"
source = "git+https://github.com/pingcap/kvproto.git#08fddd37b0f5526219ac9730c51d4689cf3b6d33"
dependencies = [
 "futures 0.3.28",
 "grpcio",
 "protobuf",
 "protobuf-build",
 "raft-proto",
]

[[package]]
name = "lazy_static"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbd2bcb4c963f2ddae06a2efc7e9f3591312473c50c6685e1f298068316e66fe"
dependencies = [
 "spin 0.9.8",
]

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "levenshtein_automata"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c2cdeb66e45e9f36bfad5bbdb4d2384e70936afbee843c6f6543f0c551ebb25"

[[package]]
name = "lexical-core"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92912c4af2e7d9075be3e5e3122c4d7263855fa6cce34fbece4dd08e5884624d"
dependencies = [
 "lexical-parse-float",
 "lexical-parse-integer",
 "lexical-util",
 "lexical-write-float",
 "lexical-write-integer",
]

[[package]]
name = "lexical-parse-float"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f518eed87c3be6debe6d26b855c97358d8a11bf05acec137e5f53080f5ad2dd8"
dependencies = [
 "lexical-parse-integer",
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "lexical-parse-integer"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afc852ec67c6538bbb2b9911116a385b24510e879a69ab516e6a151b15a79168"
dependencies = [
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "lexical-util"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c72a9d52c5c4e62fa2cdc2cb6c694a39ae1382d9c2a17a466f18e272a0930eb1"
dependencies = [
 "static_assertions",
]

[[package]]
name = "lexical-write-float"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a89ec1d062e481210c309b672f73a0567b7855f21e7d2fae636df44d12e97f9"
dependencies = [
 "lexical-util",
 "lexical-write-integer",
 "static_assertions",
]

[[package]]
name = "lexical-write-integer"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "094060bd2a7c2ff3a16d5304a6ae82727cb3cc9d1c70f813cc73f744c319337e"
dependencies = [
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "libc"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e32a70cf75e5846d53a673923498228bbec6a8624708a9ea5645f075d6276122"

[[package]]
name = "libc"
version = "0.2.174"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1171693293099992e19cddea4e8b849964e9846f4acee11b3948bcc337be8776"

[[package]]
name = "libflate"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45d9dfdc14ea4ef0900c1cddbc8dcd553fbaacd8a4a282cf4018ae9dd04fb21e"
dependencies = [
 "adler32",
 "core2",
 "crc32fast",
 "dary_heap",
 "libflate_lz77",
]

[[package]]
name = "libflate_lz77"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6e0d73b369f386f1c44abd9c570d5318f55ccde816ff4b562fa452e5182863d"
dependencies = [
 "core2",
 "hashbrown 0.14.5",
 "rle-decode-fast",
]

[[package]]
name = "libfuzzer-sys"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcf184a4b6b274f82a5df6b357da6055d3e82272327bba281c28bbba6f1664ef"
dependencies = [
 "arbitrary 0.4.7",
 "cc",
]

[[package]]
name = "libloading"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f84d96438c15fcd6c3f244c8fce01d1e2b9c6b5623e9c711dc9286d8fc92d6a"
dependencies = [
 "cfg-if 1.0.0",
 "winapi 0.3.9",
]

[[package]]
name = "libm"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8355be11b20d696c8f18f6cc018c4e372165b1fa8126cef092399c9951984ffa"

[[package]]
name = "libmimalloc-sys"
version = "0.1.39"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23aa6811d3bd4deb8a84dde645f943476d13b248d818edcf8ce0b2f37f036b44"
dependencies = [
 "cc",
 "libc 0.2.174",
]

[[package]]
name = "librocksdb_sys"
version = "0.1.0"
source = "git+https://github.com/tikv/rust-rocksdb.git#489433e1cbaba553500c5c9145bf7290f58d9e73"
dependencies = [
 "bindgen 0.65.1",
 "bzip2-sys",
 "cc",
 "cmake",
 "libc 0.2.174",
 "libtitan_sys",
 "libz-sys",
 "lz4-sys",
 "openssl-sys",
 "snappy-sys",
 "tikv-jemalloc-sys",
 "zstd-sys",
]

[[package]]
name = "libtitan_sys"
version = "0.0.1"
source = "git+https://github.com/tikv/rust-rocksdb.git#489433e1cbaba553500c5c9145bf7290f58d9e73"
dependencies = [
 "bzip2-sys",
 "cc",
 "cmake",
 "libc 0.2.174",
 "libz-sys",
 "lz4-sys",
 "snappy-sys",
 "zstd-sys",
]

[[package]]
name = "libz-sys"
version = "1.1.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b70e7a7df205e92a1a4cd9aaae7898dac0aa555503cc0a649494d0d60e7651d"
dependencies = [
 "cc",
 "libc 0.2.174",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "link-cplusplus"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d240c6f7e1ba3a28b0249f774e6a9dd0175054b52dfbb61b16eb8505c3785c9"
dependencies = [
 "cc",
]

[[package]]
name = "linked-hash-map"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fb9b38af92608140b86b693604b9ffcc5824240a484d1ecd4795bacb2fe88f3"

[[package]]
name = "linked_hash_set"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47186c6da4d81ca383c7c47c1bfc80f4b95f4720514d860a5407aaf4233f9588"
dependencies = [
 "linked-hash-map",
]

[[package]]
name = "linux-raw-sys"
version = "0.4.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d26c52dbd32dccf2d10cac7725f8eae5296885fb5703b261f7d0a0739ec807ab"

[[package]]
name = "load_data"
version = "0.0.1"
dependencies = [
 "api_version",
 "bytes",
 "chrono",
 "cloud_encryption",
 "dashmap 4.0.2",
 "encryption",
 "futures 0.3.28",
 "hex 0.4.3",
 "http",
 "hyper",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "pd_client",
 "prometheus",
 "proptest",
 "protobuf",
 "rand 0.8.5",
 "rfengine",
 "rfstore",
 "security",
 "serde",
 "serde_derive",
 "serde_json",
 "slog",
 "slog-global",
 "tempfile",
 "test_pd_client",
 "thiserror",
 "tidb_query_datatype",
 "tikv-client",
 "tikv_util",
 "tokio",
]

[[package]]
name = "lock_api"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07af8b9cdd281b7915f413fa73f29ebd5d55d0d3f0155584dade1ff18cea1b17"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13dc2df351e3202783a1fe0d44375f7295ffb4049267b0f3018346dc122a1d94"

[[package]]
name = "log_wrappers"
version = "0.0.1"
dependencies = [
 "hex 0.4.3",
 "protobuf",
 "slog",
 "slog-term",
 "tikv_alloc",
]

[[package]]
name = "lru"
version = "0.12.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "234cf4f4a04dc1f57e24b96cc0cd600cf2af460d4161ac5ecdd0af8e1f3b2a38"
dependencies = [
 "hashbrown 0.15.4",
]

[[package]]
name = "lz4"
version = "1.24.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e9e2dd86df36ce760a60f6ff6ad526f7ba1f14ba0356f8254fb6905e6494df1"
dependencies = [
 "libc 0.2.174",
 "lz4-sys",
]

[[package]]
name = "lz4-sys"
version = "1.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57d27b317e207b10f69f5e75494119e391a96f48861ae870d1da6edac98ca900"
dependencies = [
 "cc",
 "libc 0.2.174",
]

[[package]]
name = "lz4_flex"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75761162ae2b0e580d7e7c390558127e5f01b4194debd6221fd8c207fc80e3f5"

[[package]]
name = "maligned"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e88c3cbe8288f77f293e48a28b3232e3defd203a6d839fa7f68ea4329e83464"

[[package]]
name = "match-template"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c334ac67725febd94c067736ac46ef1c7cacf1c743ca14b9f917c2df2c20acd8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "matches"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ffc5c5338469d4d3ea17d269fa8ea3512ad247247c30bd2df69e68309ed0a08"

[[package]]
name = "matchit"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73cbba799671b762df5a175adf59ce145165747bb891505c43d09aefbbf38beb"

[[package]]
name = "matchit"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b87248edafb776e59e6ee64a79086f65890d3510f2c656c000bf2a7e8a0aea40"

[[package]]
name = "maybe-async"
version = "0.2.6"
source = "git+https://github.com/pingyu/maybe-async-rs?branch=cse#38a743983ac75f925260f2449e360f5a37b803fa"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "md-5"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5a279bb9607f9f53c22d496eade00d138d1bdcccd07d74650387cf94942a15"
dependencies = [
 "block-buffer 0.9.0",
 "digest 0.9.0",
 "opaque-debug",
]

[[package]]
name = "md5"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "490cc448043f947bae3cbee9c203358d62dbee0db12107a74be5c30ccfd09771"

[[package]]
name = "measure_time"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbefd235b0aadd181626f281e1d684e116972988c14c264e42069d5e8a5775cc"
dependencies = [
 "instant",
 "log",
]

[[package]]
name = "memchr"
version = "2.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f665ee40bc4a3c5590afb1e9677db74a508659dfd71e126420da8274909a0167"

[[package]]
name = "memmap2"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "057a3db23999c867821a7a59feb06a578fcb03685e983dff90daf9e7d24ac08f"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "memmap2"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd3f7eed9d3848f8b98834af67102b720745c4ec028fcd0aa0239277e7de374f"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "memoffset"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59accc507f1338036a0477ef61afdae33cde60840f4dfe481319ce3ad116ddf9"
dependencies = [
 "autocfg",
]

[[package]]
name = "memoffset"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5de893c32cde5f383baa4c04c5d6dbdd735cfd4a794b0debdb2bb1b421da5ff4"
dependencies = [
 "autocfg",
]

[[package]]
name = "memory_trace_macros"
version = "0.1.0"
dependencies = [
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "merged_engine"
version = "0.1.0"
dependencies = [
 "api_version",
 "bytes",
 "cloud_encryption",
 "collections",
 "crc32fast",
 "file_system",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "log",
 "native_br",
 "pd_client",
 "protobuf",
 "raft-proto",
 "resolved_ts",
 "rfengine",
 "rfenginepb",
 "rfstore",
 "security",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "slog",
 "slog-global",
 "slog-term",
 "thiserror",
 "tikv",
 "tikv_util",
]

[[package]]
name = "mimalloc"
version = "0.1.39"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa01922b5ea280a911e323e4d2fd24b7fe5cc4042e0d2cda3c40775cdc4bdc9c"
dependencies = [
 "libmimalloc-sys",
]

[[package]]
name = "mime"
version = "0.3.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a60c7ce501c71e03a9c9c0d35b861413ae925bd979cc7a4e30d060069aaac8d"

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a92518e98c078586bc6c934028adcca4c92a53d6a958196de835170a01d84e4b"
dependencies = [
 "adler",
 "autocfg",
]

[[package]]
name = "miniz_oxide"
version = "0.8.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fa76a2c86f704bdb222d66965fb3d63269ce38518b83cb0575fca855ebb6316"
dependencies = [
 "adler2",
]

[[package]]
name = "mio"
version = "0.6.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4afd66f5b91bf2a3bc13fad0e21caedac168ca4c707504e75585648ae80e4cc4"
dependencies = [
 "cfg-if 0.1.10",
 "fuchsia-zircon",
 "fuchsia-zircon-sys",
 "iovec",
 "kernel32-sys",
 "libc 0.2.174",
 "log",
 "miow",
 "net2",
 "slab",
 "winapi 0.2.8",
]

[[package]]
name = "mio"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d732bc30207a6423068df043e3d02e0735b155ad7ce1a6f76fe2baa5b158de"
dependencies = [
 "libc 0.2.174",
 "log",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.42.0",
]

[[package]]
name = "mio-extras"
version = "2.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52403fe290012ce777c4626790c8951324a2b9e3316b3143779c72b029742f19"
dependencies = [
 "lazycell",
 "log",
 "mio 0.6.23",
 "slab",
]

[[package]]
name = "miow"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebd808424166322d4a38da87083bfddd3ac4c131334ed55856112eb06d46944d"
dependencies = [
 "kernel32-sys",
 "net2",
 "winapi 0.2.8",
 "ws2_32-sys",
]

[[package]]
name = "mmap"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bc85448a6006dd2ba26a385a564a8a0f1f2c7e78c70f1a70b2e0f4af286b823"
dependencies = [
 "libc 0.1.12",
 "tempdir",
]

[[package]]
name = "mnt"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1587ebb20a5b04738f16cffa7e2526f1b8496b84f92920facd518362ff1559eb"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "more-asserts"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0debeb9fcf88823ea64d64e4a815ab1643f33127d995978e099942ce38f25238"

[[package]]
name = "multimap"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97fbd5d00e0e37bfb10f433af8f5aaf631e739368dc9fc28286ca81ca4948dc"
dependencies = [
 "serde",
]

[[package]]
name = "multiversion"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "025c962a3dd3cc5e0e520aa9c612201d127dcdf28616974961a649dca64f5373"
dependencies = [
 "multiversion-macros",
]

[[package]]
name = "multiversion-macros"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8a3e2bde382ebf960c1f3e79689fa5941625fe9bf694a1cb64af3e85faff3af"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "mur3"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97af489e1e21b68de4c390ecca6703318bc1aa16e9733bcb62c089b73c6fbb1b"

[[package]]
name = "murmurhash32"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2195bf6aa996a481483b29d62a7663eed3fe39600c460e323f8ff41e90bdd89b"

[[package]]
name = "native-tls"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8d96b2e1c8da3957d58100b09f102c6d9cfdfced01b7ec5a8974044bb09dbd4"
dependencies = [
 "lazy_static",
 "libc 0.2.174",
 "log",
 "openssl",
 "openssl-probe",
 "openssl-sys",
 "schannel",
 "security-framework",
 "security-framework-sys",
 "tempfile",
]

[[package]]
name = "native_br"
version = "0.0.1"
dependencies = [
 "ahash 0.8.11",
 "api_version",
 "bstr",
 "byteorder",
 "bytes",
 "chrono",
 "cloud_encryption",
 "cloud_server",
 "collections",
 "concurrency_manager",
 "dashmap 4.0.2",
 "engine_traits",
 "etcd-client",
 "fail 0.5.0",
 "file_system",
 "futures 0.3.28",
 "grpcio",
 "hex 0.4.3",
 "http",
 "hyper",
 "itertools 0.10.5",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "regex",
 "rfengine",
 "rfenginepb",
 "rfstore",
 "security",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "slog",
 "slog-global",
 "slog-term",
 "tempdir",
 "tempfile",
 "test_cloud_server",
 "test_pd_client",
 "test_util",
 "thiserror",
 "tikv",
 "tikv-client",
 "tikv_util",
 "tokio",
 "txn_types",
 "url",
]

[[package]]
name = "net2"
version = "0.2.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "391630d12b68002ae1e25e8f974306474966550ad82dac6886fb8910c19568ae"
dependencies = [
 "cfg-if 0.1.10",
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "nix"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83450fe6a6142ddd95fb064b746083fc4ef1705fe81f64a64e1d4b39f54a1055"
dependencies = [
 "bitflags 1.3.2",
 "cc",
 "cfg-if 0.1.10",
 "libc 0.2.174",
]

[[package]]
name = "nix"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa52e972a9a719cecb6864fb88568781eb706bac2cd1d4f04a648542dbf78069"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "memoffset 0.6.4",
]

[[package]]
name = "nix"
version = "0.25.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f346ff70e7dbfd675fe90590b92d59ef2de15a8779ae305ebcbfd3f0caf59be4"
dependencies = [
 "autocfg",
 "bitflags 1.3.2",
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "memoffset 0.6.4",
 "pin-utils",
]

[[package]]
name = "nix"
version = "0.26.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "598beaf3cc6fdd9a5dfb1630c2800c7acd31df7aaf0f565796fba2b53ca1af1b"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if 1.0.0",
 "libc 0.2.174",
]

[[package]]
name = "nom"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf51a729ecf40266a2368ad335a5fdde43471f545a967109cd62146ecf8b66ff"

[[package]]
name = "nom"
version = "4.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ad2a91a8e869eeb30b9cb3119ae87773a8f4ae617f41b1eb9c154b2905f7bd6"
dependencies = [
 "memchr",
 "version_check 0.1.5",
]

[[package]]
name = "nom"
version = "5.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08959a387a676302eebf4ddbcbc611da04285579f76f88ee0506c63b1a61dd4b"
dependencies = [
 "memchr",
 "version_check 0.9.4",
]

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "notify"
version = "4.0.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae03c8c853dba7bfd23e571ff0cff7bc9dceb40a4cd684cd1681824183f45257"
dependencies = [
 "bitflags 1.3.2",
 "filetime",
 "fsevent",
 "fsevent-sys",
 "inotify",
 "libc 0.2.174",
 "mio 0.6.23",
 "mio-extras",
 "walkdir",
 "winapi 0.3.9",
]

[[package]]
name = "ntapi"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f26e041cd983acbc087e30fcba770380cfa352d0e392e175b2344ebaf7ea0602"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "ntapi"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a3895c6391c39d7fe7ebc444a87eb2991b2a0bc718fdabd071eec617fc68e4"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "num"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab3e176191bc4faad357e3122c4747aa098ac880e88b168f106386128736cf4a"
dependencies = [
 "num-complex 0.3.0",
 "num-integer",
 "num-iter",
 "num-rational 0.3.0",
 "num-traits",
]

[[package]]
name = "num"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43db66d1170d347f9a065114077f7dccb00c1b9478c89384490a3425279a4606"
dependencies = [
 "num-bigint 0.4.3",
 "num-complex 0.4.1",
 "num-integer",
 "num-iter",
 "num-rational 0.4.0",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f6f7833f2cbf2360a6cfd58cd41a53aa7a90bd4c202f5b1c7dd2ed73c57b2c3"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f93ab6289c7b344a8a9f60f88d80aa20032336fe78da341afc91c8a2341fc75f"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-bigint-dig"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc84195820f291c7697304f3cbdadd1cb7199c0efc917ff5eafd71225c136151"
dependencies = [
 "byteorder",
 "lazy_static",
 "libm",
 "num-integer",
 "num-iter",
 "num-traits",
 "rand 0.8.5",
 "smallvec",
 "zeroize",
]

[[package]]
name = "num-complex"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b05ad05bd8977050b171b3f6b48175fea6e0565b7981059b486075e1026a9fb5"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-complex"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fbc387afefefd5e9e39493299f3069e14a140dd34dc19b4c1c1a8fddb6a790"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-conv"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51d515d32fb182ee37cda2ccdcb92950d6a3c2893aa280e540671c2cd0f3b1d9"

[[package]]
name = "num-derive"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c8b15b261814f992e33760b1fca9fe8b693d8a65299f20c9901688636cfb746"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "num-format"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a652d9771a63711fd3c3deb670acfbe5c30a4072e664d7a3bf5a9e1056ac72c3"
dependencies = [
 "arrayvec",
 "itoa 1.0.6",
]

[[package]]
name = "num-integer"
version = "0.1.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2cc698a63b549a70bc047073d2949cce27cd1c7b0a4a862d08a8031bc2801db"
dependencies = [
 "autocfg",
 "num-traits",
]

[[package]]
name = "num-iter"
version = "0.1.42"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2021c8337a54d21aca0d59a92577a029af9431cb59b909b03252b9c164fad59"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5b4d7360f362cfb50dde8143501e6940b22f644be75a4cc90b2d81968908138"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d41702bd167c2df5520b384281bc111a4b5efcf7fbc4c9c222c815b07e0a6a6a"
dependencies = [
 "autocfg",
 "num-bigint 0.4.3",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "071dfc062690e90b734c0b2273ce72ad0ffa95f0c74596bc250dcfd960262841"
dependencies = [
 "autocfg",
 "libm",
]

[[package]]
name = "num_cpus"
version = "1.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19e64526ebdee182341572e50e9ad03965aa510cd94427a4549448f285e957a1"
dependencies = [
 "hermit-abi 0.1.3",
 "libc 0.2.174",
]

[[package]]
name = "oauth2"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80e47cfc4c0a1a519d9a025ebfbac3a2439d1b5cdf397d72dcb79b11d9920dab"
dependencies = [
 "base64 0.13.0",
 "chrono",
 "getrandom 0.2.16",
 "http",
 "rand 0.8.5",
 "reqwest",
 "serde",
 "serde_json",
 "serde_path_to_error",
 "sha2 0.9.1",
 "thiserror",
 "url",
]

[[package]]
name = "object"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39f37e50073ccad23b6d09bcb5b263f4e76d3bb6038e4a3c08e52162ffa8abc2"
dependencies = [
 "memchr",
]

[[package]]
name = "once_cell"
version = "1.21.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42f5e15c9953c5e4ccceeb2e7382a716482c34515315f7b03532b8b4e8393d2d"

[[package]]
name = "oneshot"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4ce411919553d3f9fa53a0880544cda985a112117a0444d5ff1e870a893d6ea"

[[package]]
name = "online_config"
version = "0.1.0"
dependencies = [
 "online_config_derive",
 "serde",
 "serde_derive",
 "toml",
]

[[package]]
name = "online_config_derive"
version = "0.1.0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "oorandom"
version = "11.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ab1bc2a289d34bd04a330323ac98a1b4bc82c9d9fcb1e66b63caa84da26b575"

[[package]]
name = "opaque-debug"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "624a8340c38c1b80fd549087862da4ba43e08858af025b236e509b6649fc13d5"

[[package]]
name = "openssl"
version = "0.10.72"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fedfea7d58a1f73118430a55da6a286e7b044961736ce96a16a17068ea25e5da"
dependencies = [
 "bitflags 2.5.0",
 "cfg-if 1.0.0",
 "foreign-types",
 "libc 0.2.174",
 "once_cell",
 "openssl-macros",
 "openssl-sys",
]

[[package]]
name = "openssl-macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a948666b637a0f465e8564c73e89d4dde00d72d4d473cc972f390fc3dcee7d9c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "openssl-probe"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77af24da69f9d9341038eba93a073b1fdaaa1b788221b00a69bce9e762cb32de"

[[package]]
name = "openssl-src"
version = "300.3.1****.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7259953d42a81bf137fbbd73bd30a8e1914d6dce43c2b90ed575783a22608b91"
dependencies = [
 "cc",
]

[[package]]
name = "openssl-sys"
version = "0.9.107"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8288979acd84749c744a9014b4382d42b8f7b2592847b5afb2ed29e5d16ede07"
dependencies = [
 "cc",
 "libc 0.2.174",
 "openssl-src",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "ordered-float"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7940cf2ca942593318d07fcf2596cdca60a85c9e7fab408a5e21a4f9dcd40d87"
dependencies = [
 "num-traits",
]

[[package]]
name = "ordered-float"
version = "5.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2c1f9f56e534ac6a9b8a4600bdf0f530fb393b5f393e7b4d03489c3cf0c3f01"
dependencies = [
 "num-traits",
]

[[package]]
name = "os_str_bytes"
version = "6.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e22443d1643a904602595ba1cd8f7d896afe56d26712531c5ff73a15b2fbf64"

[[package]]
name = "overload_protector"
version = "0.1.0"
dependencies = [
 "online_config",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tikv_util",
 "txn_types",
]

[[package]]
name = "ownedbytes"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3a059efb063b8f425b948e042e6b9bd85edfe60e913630ed727b23e2dfcc558"
dependencies = [
 "stable_deref_trait",
]

[[package]]
name = "page_size"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eebde548fbbf1ea81a99b128872779c437752fb99f217c45245e1a61dcd9edcd"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "panic_hook"
version = "0.0.1"

[[package]]
name = "papaya"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f92dd0b07c53a0a0c764db2ace8c541dc47320dad97c2200c2a637ab9dd2328f"
dependencies = [
 "equivalent",
 "seize",
]

[[package]]
name = "parking_lot"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d7744ac029df22dca6284efe4e898991d28e3085c706c972bcd7da4a27a15eb"
dependencies = [
 "instant",
 "lock_api",
 "parking_lot_core 0.8.3",
]

[[package]]
name = "parking_lot"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87f5ec2493a61ac0506c0f4199f99070cbe83857b0337006a30f3e6719b8ef58"
dependencies = [
 "lock_api",
 "parking_lot_core 0.9.10",
]

[[package]]
name = "parking_lot_core"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa7a782938e745763fe6907fc6ba86946d72f49fe7e21de074e08128a99fb018"
dependencies = [
 "cfg-if 1.0.0",
 "instant",
 "libc 0.2.174",
 "redox_syscall 0.2.11",
 "smallvec",
 "winapi 0.3.9",
]

[[package]]
name = "parking_lot_core"
version = "0.9.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e401f977ab385c9e4e3ab30627d6f26d00e2c73eef317493c4ec6d468726cf8"
dependencies = [
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "redox_syscall 0.5.7",
 "smallvec",
 "windows-targets 0.52.6",
]

[[package]]
name = "parse-zoneinfo"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c705f256449c60da65e11ff6626e0c16a0a0b96aaa348de61376b249bc340f41"
dependencies = [
 "regex",
]

[[package]]
name = "paste"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57c0d7b74b563b49d38dae00a0c37d4d6de9b432382b2892f0574ddcae73fd0a"

[[package]]
name = "pd_client"
version = "0.1.0"
dependencies = [
 "async-trait",
 "bstr",
 "bytes",
 "cloud_encryption",
 "collections",
 "dashmap 4.0.2",
 "error_code",
 "fail 0.5.0",
 "futures 0.3.28",
 "grpcio",
 "hex 0.4.3",
 "http",
 "kvproto",
 "lazy_static",
 "log",
 "log_wrappers",
 "prometheus",
 "security",
 "semver 0.10.0",
 "serde",
 "serde_derive",
 "serde_json",
 "slog",
 "slog-global",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
 "url",
 "yatp",
]

[[package]]
name = "pdqselect"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ec91767ecc0a0bbe558ce8c9da33c068066c57ecc8bb8477ef8c1ad3ef77c27"

[[package]]
name = "peeking_take_while"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19b17cddbe7ec3f8bc800887bab5e717348c95ea2ca0b1bf0837fb964dc67099"

[[package]]
name = "pem"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8835c273a76a90455d7344889b0964598e3316e2a79ede8e36f16bdcf2228b8"
dependencies = [
 "base64 0.13.0",
]

[[package]]
name = "pem-rfc7468"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01de5d978f34aa4b2296576379fcc416034702fd94117c56ffd8a1a767cefb30"
dependencies = [
 "base64ct",
]

[[package]]
name = "percent-encoding"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4fd5641d01c8f18a23da7b6fe29298ff4b55afcccdf78973b24cf3175fee32e"

[[package]]
name = "perfcnt"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8f94885300e262ef461aa9fd1afbf7df3caf9e84e271a74925d1c6c8b24830f"
dependencies = [
 "bitflags 1.3.2",
 "byteorder",
 "libc 0.2.174",
 "mmap",
 "nom 4.2.3",
 "phf 0.9.0",
 "x86",
]

[[package]]
name = "pest"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10f4872ae94d7b90ae48754df22fd42ad52ce740b8f370b03da4835417403e53"
dependencies = [
 "ucd-trie",
]

[[package]]
name = "petgraph"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a13a2fa9d0b63e5f22328828741e523766fff0ee9e779316902290dff3f824f"
dependencies = [
 "fixedbitset",
 "indexmap 1.6.2",
]

[[package]]
name = "phf"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2ac8b67553a7ca9457ce0e526948cad581819238f4a9d1ea74545851fa24f37"
dependencies = [
 "phf_shared 0.9.0",
]

[[package]]
name = "phf"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd6780a80ae0c52cc120a26a1a42c1ae51b247a253e4e06113d23d2c2edd078"
dependencies = [
 "phf_shared 0.11.3",
]

[[package]]
name = "phf_codegen"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "963adb11cf22ee65dfd401cf75577c1aa0eca58c0b97f9337d2da61d3e640503"
dependencies = [
 "phf_generator 0.9.1",
 "phf_shared 0.9.0",
]

[[package]]
name = "phf_codegen"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aef8048c789fa5e851558d709946d6d79a8ff88c0440c587967f8e94bfb1216a"
dependencies = [
 "phf_generator 0.11.3",
 "phf_shared 0.11.3",
]

[[package]]
name = "phf_generator"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d43f3220d96e0080cc9ea234978ccd80d904eafb17be31bb0f76daaea6493082"
dependencies = [
 "phf_shared 0.9.0",
 "rand 0.8.5",
]

[[package]]
name = "phf_generator"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c80231409c20246a13fddb31776fb942c38553c51e871f8cbd687a4cfb5843d"
dependencies = [
 "phf_shared 0.11.3",
 "rand 0.8.5",
]

[[package]]
name = "phf_shared"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a68318426de33640f02be62b4ae8eb1261be2efbc337b60c54d845bf4484e0d9"
dependencies = [
 "siphasher 0.3.3",
]

[[package]]
name = "phf_shared"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67eabc2ef2a60eb7faa00097bd1ffdb5bd28e62bf39990626a582201b7a754e5"
dependencies = [
 "siphasher 1.0.1",
]

[[package]]
name = "pin-project"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78203e83c48cffbe01e4a2d35d566ca4de445d79a85372fc64e378bfc812a260"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "710faf75e1b33345361201d36d04e98ac1ed8909151a017ed384700836104c74"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "pin-project-lite"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0a7ae3ac2f1173085d398531c705756c94a4c56843785df85a60c1a0afac116"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "pkcs1"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a78f66c04ccc83dd4486fd46c33896f4e17b24a7a3a6400dedc48ed0ddd72320"
dependencies = [
 "der",
 "pkcs8",
 "zeroize",
]

[[package]]
name = "pkcs8"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cabda3fb821068a9a4fab19a683eac3af12edf0f34b94a8be53c4972b8149d0"
dependencies = [
 "der",
 "spki",
 "zeroize",
]

[[package]]
name = "pkg-config"
version = "0.3.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72d5370d90f49f70bd033c3d75e87fc529fbfff9d6f7cccef07d6170079d91ea"

[[package]]
name = "plotters"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a3fd9ec30b9749ce28cd91f255d569591cdf937fe280c312143e3c4bad6f2a"
dependencies = [
 "num-traits",
 "plotters-backend",
 "plotters-svg",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "plotters-backend"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b07fffcddc1cb3a1de753caa4e4df03b79922ba43cf882acc1bdd7e8df9f4590"

[[package]]
name = "plotters-svg"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b38a02e23bd9604b842a812063aec4ef702b57989c37b655254bb61c471ad211"
dependencies = [
 "plotters-backend",
]

[[package]]
name = "pnet_base"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4df28acf2fcc77436dd2b91a9a0c2bb617f9ca5f2acefee1a4135058b9f9801f"

[[package]]
name = "pnet_datalink"
version = "0.23.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d27361d7578b410d0eb5fe815c2b2105b01ab770a7c738cb9a231457a809fcc7"
dependencies = [
 "ipnetwork",
 "libc 0.2.174",
 "pnet_base",
 "pnet_sys",
 "winapi 0.2.8",
]

[[package]]
name = "pnet_sys"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82f881a6d75ac98c5541db6144682d1773bb14c6fc50c6ebac7086c8f7f23c29"
dependencies = [
 "libc 0.2.174",
 "winapi 0.2.8",
 "ws2_32-sys",
]

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "pprof"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebbe2f8898beba44815fdc9e5a4ae9c929e21c5dc29b0c774a15555f7f58d6d0"
dependencies = [
 "aligned-vec",
 "backtrace",
 "cfg-if 1.0.0",
 "findshlibs",
 "inferno",
 "libc 0.2.174",
 "log",
 "nix 0.26.4",
 "once_cell",
 "parking_lot 0.12.0",
 "protobuf",
 "protobuf-codegen-pure",
 "smallvec",
 "symbolic-demangle",
 "tempfile",
 "thiserror",
]

[[package]]
name = "ppv-lite86"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85eae3c4ed2f50dcfe72643da4befc30deadb458a9b590d720cde2f2b1e97da9"
dependencies = [
 "zerocopy 0.8.26",
]

[[package]]
name = "prettyplease"
version = "0.1.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e97e3215779627f01ee256d2fad52f3d95e8e1c11e9fc6fd08f7cd455d5d5c78"
dependencies = [
 "proc-macro2",
 "syn 1.0.107",
]

[[package]]
name = "prettyplease"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c64d9ba0963cdcea2e1b2230fbae2bab30eb25a174be395c41e764bfb65dd62"
dependencies = [
 "proc-macro2",
 "syn 2.0.104",
]

[[package]]
name = "proc-macro-error"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c"
dependencies = [
 "proc-macro-error-attr",
 "proc-macro2",
 "quote",
 "syn 1.0.107",
 "version_check 0.9.4",
]

[[package]]
name = "proc-macro-error-attr"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869"
dependencies = [
 "proc-macro2",
 "quote",
 "version_check 0.9.4",
]

[[package]]
name = "proc-macro2"
version = "1.0.95"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02b3e5e68a3a1a02aad3ec490a98007cbc13c37cbe84a3cd7b8e406d76e7f778"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "procfs"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0941606b9934e2d98a3677759a971756eb821f75764d0e0d26946d08e74d9104"
dependencies = [
 "bitflags 1.3.2",
 "byteorder",
 "hex 0.4.3",
 "lazy_static",
 "libc 0.2.174",
]

[[package]]
name = "procinfo"
version = "0.4.2"
source = "git+https://github.com/tikv/procinfo-rs?rev=6599eb9dca74229b2c1fcc44118bef7eff127128#6599eb9dca74229b2c1fcc44118bef7eff127128"
dependencies = [
 "byteorder",
 "libc 0.2.174",
 "nom 2.2.1",
 "rustc_version 0.2.3",
]

[[package]]
name = "profiler"
version = "0.0.1"
dependencies = [
 "callgrind",
 "gperftools",
 "lazy_static",
 "tikv_alloc",
 "valgrind_request",
]

[[package]]
name = "prometheus"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7f64969ffd5dd8f39bd57a68ac53c163a095ed9d0fb707146da1b27025a3504"
dependencies = [
 "cfg-if 1.0.0",
 "fnv",
 "lazy_static",
 "libc 0.2.174",
 "memchr",
 "parking_lot 0.11.1",
 "protobuf",
 "reqwest",
 "thiserror",
]

[[package]]
name = "prometheus-static-metric"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8f30cdb09c39930b8fa5e0f23cbb895ab3f766b187403a0ba0956fc1ef4f0e5"
dependencies = [
 "lazy_static",
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "promptly"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b99cfb0289110d969dd21637cfbc922584329bc9e5037c5e576325c615658509"
dependencies = [
 "rustyline",
]

[[package]]
name = "proptest"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31b476131c3c86cb68032fdc5cb6d5a1045e3e42d96b69fa599fd77701e1f5bf"
dependencies = [
 "bit-set",
 "bit-vec",
 "bitflags 2.5.0",
 "lazy_static",
 "num-traits",
 "rand 0.8.5",
 "rand_chacha 0.3.0",
 "rand_xorshift",
 "regex-syntax",
 "rusty-fork",
 "tempfile",
 "unarray",
]

[[package]]
name = "prost"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b82eaa1d779e9a4bc1c3217db8ffbeabaae1dca241bf70183242128d48681cd"
dependencies = [
 "bytes",
 "prost-derive 0.11.9",
]

[[package]]
name = "prost"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4fdd22f3b9c31b53c060df4a0613a1c7f062d4115a2b984dd15b1858f7e340d"
dependencies = [
 "bytes",
 "prost-derive 0.12.1",
]

[[package]]
name = "prost"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2796faa41db3ec313a31f7624d9286acf277b52de526150b7e69f3debf891ee5"
dependencies = [
 "bytes",
 "prost-derive 0.13.5",
]

[[package]]
name = "prost-build"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "119533552c9a7ffacc21e099c24a0ac8bb19c2a2a3f363de84cd9b844feab270"
dependencies = [
 "bytes",
 "heck 0.4.0",
 "itertools 0.10.5",
 "lazy_static",
 "log",
 "multimap",
 "petgraph",
 "prettyplease 0.1.23",
 "prost 0.11.9",
 "prost-types 0.11.9",
 "regex",
 "syn 1.0.107",
 "tempfile",
 "which",
]

[[package]]
name = "prost-build"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be769465445e8c1474e9c5dac2018218498557af32d9ed057325ec9a41ae81bf"
dependencies = [
 "heck 0.5.0",
 "itertools 0.14.0",
 "log",
 "multimap",
 "once_cell",
 "petgraph",
 "prettyplease 0.2.12",
 "prost 0.13.5",
 "prost-types 0.13.5",
 "regex",
 "syn 2.0.104",
 "tempfile",
]

[[package]]
name = "prost-derive"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d2d8d10f3c6ded6da8b05b5fb3b8a5082514344d56c9f871412d29b4e075b4"
dependencies = [
 "anyhow",
 "itertools 0.10.5",
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "prost-derive"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "265baba7fabd416cf5078179f7d2cbeca4ce7a9041111900675ea7c4cb8a4c32"
dependencies = [
 "anyhow",
 "itertools 0.11.0",
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "prost-derive"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a56d757972c98b346a9b766e3f02746cde6dd1cd1d1d563472929fdd74bec4d"
dependencies = [
 "anyhow",
 "itertools 0.14.0",
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "prost-types"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "213622a1460818959ac1181aaeb2dc9c7f63df720db7d788b3e24eacd1983e13"
dependencies = [
 "prost 0.11.9",
]

[[package]]
name = "prost-types"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52c2c1bf36ddb1a1c396b3601a3cec27c2462e45f07c386894ec3ccf5332bd16"
dependencies = [
 "prost 0.13.5",
]

[[package]]
name = "protobuf"
version = "2.8.0"
source = "git+https://github.com/pingcap/rust-protobuf?branch=v2.8#6642ebaae4352ea01bf00e160480d8da966d3109"
dependencies = [
 "bytes",
 "heck 0.3.1",
 "hex 0.3.2",
]

[[package]]
name = "protobuf-build"
version = "0.15.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c852d9625b912c3e50480cdc701f60f49890b5d7ad46198dd583600f15e7c6ec"
dependencies = [
 "bitflags 1.3.2",
 "grpcio-compiler",
 "protobuf",
 "protobuf-codegen",
 "protobuf-src",
 "regex",
]

[[package]]
name = "protobuf-codegen"
version = "2.8.0"
source = "git+https://github.com/pingcap/rust-protobuf?branch=v2.8#6642ebaae4352ea01bf00e160480d8da966d3109"
dependencies = [
 "heck 0.3.1",
 "protobuf",
]

[[package]]
name = "protobuf-codegen-pure"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00993dc5fbbfcf9d8a005f6b6c29fd29fd6d86deba3ae3f41fd20c624c414616"
dependencies = [
 "protobuf",
 "protobuf-codegen",
]

[[package]]
name = "protobuf-src"
version = "1.1.0+21.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7ac8852baeb3cc6fb83b93646fb93c0ffe5d14bf138c945ceb4b9948ee0e3c1"
dependencies = [
 "autotools",
]

[[package]]
name = "quick-error"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1d01941d82fa2ab50be1e79e6714289dd7cde78eba4c074bc5a4374f650dfe0"

[[package]]
name = "quick-xml"
version = "0.23.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11bafc859c6815fbaffbbbf4229ecb767ac913fecb27f9ad4343662e9ef099ea"
dependencies = [
 "memchr",
 "serde",
]

[[package]]
name = "quick-xml"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f50b1c63b38611e7d4d7f68b82d3ad0cc71a2ad2e7f61fc10f1328d917c93cd"
dependencies = [
 "memchr",
]

[[package]]
name = "quick_cache"
version = "0.6.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b450dad8382b1b95061d5ca1eb792081fb082adf48c678791fe917509596d5f"
dependencies = [
 "ahash 0.8.11",
 "equivalent",
 "hashbrown 0.15.4",
 "parking_lot 0.12.0",
]

[[package]]
name = "quote"
version = "1.0.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "291ec9ab5efd934aaf503a6466c5d5251535d108ee747472c3977cc5acc868ef"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "r-efi"
version = "5.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69cdb34c158ceb288df11e18b4bd39de994f6657d83847bdffdbd7f346754b0f"

[[package]]
name = "raft"
version = "0.7.0"
source = "git+https://github.com/tikv/raft-rs?branch=master#0d01b20312f74889a5e44ad4180aade5da2f16fa"
dependencies = [
 "bytes",
 "fxhash",
 "getset",
 "protobuf",
 "raft-proto",
 "rand 0.8.5",
 "slog",
 "thiserror",
]

[[package]]
name = "raft-engine"
version = "0.3.0"
source = "git+https://github.com/tikv/raft-engine.git#82f6da7b8dff1856483e8e72a59dda903fb2499b"
dependencies = [
 "byteorder",
 "crc32fast",
 "crossbeam",
 "fail 0.5.0",
 "fs2",
 "hashbrown 0.12.0",
 "hex 0.4.3",
 "if_chain",
 "lazy_static",
 "libc 0.2.174",
 "log",
 "lz4-sys",
 "memmap2 0.5.3",
 "nix 0.25.1",
 "num-derive",
 "num-traits",
 "parking_lot 0.12.0",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "rayon",
 "rhai",
 "scopeguard",
 "serde",
 "serde_repr",
 "strum 0.24.1",
 "thiserror",
]

[[package]]
name = "raft-engine-ctl"
version = "0.3.0"
source = "git+https://github.com/tikv/raft-engine.git#82f6da7b8dff1856483e8e72a59dda903fb2499b"
dependencies = [
 "clap 3.2.22",
 "env_logger",
 "raft-engine",
]

[[package]]
name = "raft-proto"
version = "0.7.0"
source = "git+https://github.com/tikv/raft-rs?branch=master#0d01b20312f74889a5e44ad4180aade5da2f16fa"
dependencies = [
 "bytes",
 "protobuf",
 "protobuf-build",
]

[[package]]
name = "raft_log_engine"
version = "0.0.1"
dependencies = [
 "encryption",
 "engine_traits",
 "file_system",
 "kvproto",
 "lazy_static",
 "num_cpus",
 "online_config",
 "protobuf",
 "raft",
 "raft-engine",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tikv_util",
 "time 0.1.43",
 "tracker",
]

[[package]]
name = "raftstore"
version = "0.0.1"
dependencies = [
 "batch-system",
 "bitflags 1.3.2",
 "byteorder",
 "bytes",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crc32fast",
 "crossbeam",
 "derivative",
 "encryption",
 "encryption_export",
 "engine_panic",
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "error_code",
 "fail 0.5.0",
 "file_system",
 "fs2",
 "futures 0.3.28",
 "futures-util",
 "getset",
 "grpcio-health",
 "into_other",
 "itertools 0.10.5",
 "keys",
 "kvproto",
 "lazy_static",
 "log",
 "log_wrappers",
 "memory_trace_macros",
 "online_config",
 "openssl",
 "ordered-float 2.10.0",
 "panic_hook",
 "parking_lot 0.12.0",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "raft-proto",
 "rand 0.8.5",
 "resource_metering",
 "security",
 "serde",
 "serde_derive",
 "serde_with",
 "slog",
 "slog-global",
 "smallvec",
 "sst_importer",
 "tempfile",
 "test_sst_importer",
 "thiserror",
 "tidb_query_datatype",
 "tikv_alloc",
 "tikv_util",
 "time 0.1.43",
 "tokio",
 "tracker",
 "txn_types",
 "uuid 0.8.2",
 "yatp",
]

[[package]]
name = "raftstore-v2"
version = "0.1.0"
dependencies = [
 "batch-system",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "engine_test",
 "engine_traits",
 "error_code",
 "fail 0.5.0",
 "file_system",
 "fs2",
 "futures 0.3.28",
 "keys",
 "kvproto",
 "log_wrappers",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raft-proto",
 "raftstore",
 "resource_metering",
 "slog",
 "slog-global",
 "smallvec",
 "tempfile",
 "test_pd",
 "test_util",
 "tikv_util",
 "time 0.1.43",
 "tracker",
 "txn_types",
 "yatp",
]

[[package]]
name = "rand"
version = "0.3.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64ac302d8f83c0c1974bf758f6b041c6c8ada916fbb44a609158ca8b064cc76c"
dependencies = [
 "libc 0.2.174",
 "rand 0.4.6",
]

[[package]]
name = "rand"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "552840b97013b1a26992c11eac34bdd778e464601a4c2054b5f0bff7c6761293"
dependencies = [
 "fuchsia-cprng",
 "libc 0.2.174",
 "rand_core 0.3.1",
 "rdrand",
 "winapi 0.3.9",
]

[[package]]
name = "rand"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a6b1679d49b24bbfe0c803429aa1874472f50d9b363131f0e89fc356b544d03"
dependencies = [
 "getrandom 0.1.12",
 "libc 0.2.174",
 "rand_chacha 0.2.1",
 "rand_core 0.5.1",
 "rand_hc",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc 0.2.174",
 "rand_chacha 0.3.0",
 "rand_core 0.6.2",
]

[[package]]
name = "rand"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6db2770f06117d490610c7488547d543617b21bfa07796d7a12f6f1bd53850d1"
dependencies = [
 "rand_chacha 0.9.0",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_chacha"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03a2a90da8c7523f554344f921aa97283eadf6ac484a6d2a7d0212fa7f8d6853"
dependencies = [
 "c2-chacha",
 "rand_core 0.5.1",
]

[[package]]
name = "rand_chacha"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e12735cf05c9e10bf21534da50a147b924d555dc7a547c42e6bb2d5b6017ae0d"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.2",
]

[[package]]
name = "rand_chacha"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3022b5f1df60f26e1ffddd6c66e8aa15de382ae63b3a0c1bfc0e4d3e3f325cb"
dependencies = [
 "ppv-lite86",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_core"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a6fdeb83b075e8266dcc8762c22776f6877a63111121f5f8c7411e5be7eed4b"
dependencies = [
 "rand_core 0.4.2",
]

[[package]]
name = "rand_core"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c33a3c44ca05fa6f1807d8e6743f3824e8509beca625669633be0acbdf509dc"

[[package]]
name = "rand_core"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90bde5296fc891b0cef12a6d03ddccc162ce7b2aff54160af9338f8d40df6d19"
dependencies = [
 "getrandom 0.1.12",
]

[[package]]
name = "rand_core"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34cf66eb183df1c5876e2dcf6b13d57340741e8dc255b48e40a26de954d06ae7"
dependencies = [
 "getrandom 0.2.16",
]

[[package]]
name = "rand_core"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99d9a13982dcf210057a8a78572b2217b667c3beacbf3a0d8b454f6f82837d38"
dependencies = [
 "getrandom 0.3.3",
]

[[package]]
name = "rand_distr"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32cb0b9bc82b0a0876c2dd994a7e7a2683d3e7390ca40e6886785ef0c7e3ee31"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "rand_hc"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3129af7b92a17112d59ad498c6f81eaf463253766b90396d39ea7a39d6613c"
dependencies = [
 "rand_core 0.5.1",
]

[[package]]
name = "rand_isaac"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fac4373cd91b4f55722c553fb0f286edbb81ef3ff6eec7b99d1898a4110a0b28"
dependencies = [
 "rand_core 0.6.2",
]

[[package]]
name = "rand_xorshift"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d25bf25ec5ae4a3f1b92f929810509a2f53d7dca2f50b794ff57e3face536c8f"
dependencies = [
 "rand_core 0.6.2",
]

[[package]]
name = "raw-cpuid"
version = "10.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "929f54e29691d4e6a9cc558479de70db7aa3d98cd6fe7ab86d7507aa2886b9d2"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "rayon"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c27db03db7734835b3f53954b534c91069375ce6ccaa2e065441e07d9b6cdb1"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ce3fb6ad83f861aac485e76e1985cd109d9a3713802152be56c3b1f0e0658ed"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils 0.8.21",
]

[[package]]
name = "rdrand"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "678054eb77286b51581ba43620cc911abf02758c91f93f479767aed0f90458b2"
dependencies = [
 "rand_core 0.3.1",
]

[[package]]
name = "recovery"
version = "0.1.0"
dependencies = [
 "dashmap 6.1.0",
 "http",
 "hyper",
 "lazy_static",
 "security",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "url",
]

[[package]]
name = "redox_syscall"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8380fe0152551244f0747b1bf41737e0f8a74f97a14ccefd1148187271634f3c"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_syscall"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b6dfecf2c74bce2466cabf93f6664d6998a69eb21e39f4207930065b27b771f"
dependencies = [
 "bitflags 2.5.0",
]

[[package]]
name = "redox_users"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "528532f3d801c87aec9def2add9ca802fe569e44a544afe633765267840abe64"
dependencies = [
 "getrandom 0.2.16",
 "redox_syscall 0.2.11",
]

[[package]]
name = "regex"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b544ef1b4eac5dc2db33ea63606ae9ffcfac26c1416a2806ae0bf5f56b201191"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-automata 0.4.9",
 "regex-syntax",
]

[[package]]
name = "regex-automata"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92b73c2a1770c255c240eaa4ee600df1704a38dc3feaa6e949e7fcd4f8dc09f9"
dependencies = [
 "byteorder",
]

[[package]]
name = "regex-automata"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "809e8dc61f6de73b46c85f4c96486310fe304c434cfa43669d7b40f711150908"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax",
]

[[package]]
name = "regex-syntax"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b15c43186be67a4fd63bee50d0303afffcef381492ebe2c5d87f324e1b8815c"

[[package]]
name = "relative-path"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba39f3699c378cd8970968dcbff9c43159ea4cfbd88d43c00b22f2ef10a435d2"

[[package]]
name = "remove_dir_all"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a83fa3702a688b9359eccba92d153ac33fd2e8462f9e0e3fdf155239ea7792e"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "replication_worker"
version = "0.1.0"
dependencies = [
 "api_version",
 "async-trait",
 "bytes",
 "cdc",
 "chrono",
 "dashmap 5.1.0",
 "futures 0.3.28",
 "grpcio",
 "grpcio-health",
 "http",
 "hyper",
 "k8s-openapi",
 "kube",
 "kvengine",
 "kvproto",
 "merged_engine",
 "native_br",
 "nix 0.24.3",
 "pd_client",
 "resolved_ts",
 "rfengine",
 "rfstore",
 "security",
 "semver 0.10.0",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "slog",
 "slog-global",
 "slog-term",
 "test_util",
 "thiserror",
 "tidb_query_datatype",
 "tikv",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "reqwest"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0460542b551950620a3648c6aa23318ac6b3cd779114bd873209e6e8b5eb1c34"
dependencies = [
 "base64 0.13.0",
 "bytes",
 "encoding_rs 0.8.35",
 "futures-core",
 "futures-util",
 "http",
 "http-body",
 "hyper",
 "hyper-tls",
 "ipnet",
 "js-sys",
 "lazy_static",
 "log",
 "mime",
 "native-tls",
 "percent-encoding",
 "pin-project-lite",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "tokio",
 "tokio-native-tls",
 "url",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "winreg",
]

[[package]]
name = "resolved_ts"
version = "0.0.1"
dependencies = [
 "collections",
 "concurrency_manager",
 "crossbeam",
 "engine_rocks",
 "engine_traits",
 "fail 0.5.0",
 "futures 0.3.28",
 "grpcio",
 "hex 0.4.3",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "panic_hook",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raftstore",
 "security",
 "slog",
 "slog-global",
 "tempfile",
 "test_raftstore",
 "test_sst_importer",
 "test_util",
 "thiserror",
 "tikv",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "resource_metering"
version = "0.0.1"
dependencies = [
 "collections",
 "crossbeam",
 "futures 0.3.28",
 "grpcio",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "log",
 "online_config",
 "pdqselect",
 "pin-project",
 "procinfo",
 "prometheus",
 "prometheus-static-metric",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tikv_util",
]

[[package]]
name = "rev_lines"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18eb52b6664d331053136fcac7e4883bdc6f5fc04a6aab3b0f75eafb80ab88b3"

[[package]]
name = "rfengine"
version = "0.0.1"
dependencies = [
 "api_version",
 "byteorder",
 "bytes",
 "chrono",
 "crc32c",
 "engine_traits",
 "error_code",
 "file_system",
 "fslock",
 "futures 0.3.28",
 "kvengine",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "log_wrappers",
 "lz4",
 "papaya",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "quick_cache",
 "raft",
 "raft-proto",
 "rand 0.8.5",
 "regex",
 "rfenginepb",
 "rstest",
 "serde",
 "serde_derive",
 "serde_json",
 "slog",
 "slog-global",
 "slog-term",
 "tempfile",
 "test_util",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "tracker",
]

[[package]]
name = "rfenginepb"
version = "0.1.0"
dependencies = [
 "protobuf",
 "protobuf-codegen-pure",
]

[[package]]
name = "rfstore"
version = "0.0.1"
dependencies = [
 "api_version",
 "async-trait",
 "bitflags 1.3.2",
 "byteorder",
 "bytes",
 "cloud_encryption",
 "collections",
 "concurrency_manager",
 "crc32c",
 "crossbeam",
 "dashmap 4.0.2",
 "derivative",
 "dyn-clone",
 "encryption",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "fail 0.5.0",
 "fs2",
 "futures 0.3.28",
 "futures-util",
 "grpcio",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "papaya",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "raft-proto",
 "raftstore",
 "rand 0.8.5",
 "rfengine",
 "schema",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "slog-term",
 "sst_importer",
 "thiserror",
 "tidb_query_datatype",
 "tikv_alloc",
 "tikv_util",
 "time 0.1.43",
 "tokio",
 "txn_types",
 "uuid 0.8.2",
 "yatp",
]

[[package]]
name = "rgb"
version = "0.8.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e74fdc210d8f24a7dbfedc13b04ba5764f5232754ccebfdf5fff1bad791ccbc6"
dependencies = [
 "bytemuck",
]

[[package]]
name = "rhai"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6eec3a3db30f591ece18c66b3db4c9fa26f3bce20bc821c50550968361f84333"
dependencies = [
 "ahash 0.8.11",
 "bitflags 1.3.2",
 "instant",
 "num-traits",
 "rhai_codegen",
 "smallvec",
 "smartstring",
]

[[package]]
name = "rhai_codegen"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75a39bc2aa9258b282ee5518dac493491a9c4c11a6d7361b9d2644c922fc6488"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "ring"
version = "0.16.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3053cf52e236a3ed746dfc745aa9cacf1b791d846bdaf412f60a8d7d6e17c8fc"
dependencies = [
 "cc",
 "libc 0.2.174",
 "once_cell",
 "spin 0.5.2",
 "untrusted 0.7.1",
 "web-sys",
 "winapi 0.3.9",
]

[[package]]
name = "ring"
version = "0.17.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb0205304757e5d899b9c2e448b867ffd03ae7f988002e47cd24954391394d0b"
dependencies = [
 "cc",
 "getrandom 0.2.16",
 "libc 0.2.174",
 "spin 0.9.8",
 "untrusted 0.9.0",
 "windows-sys 0.48.0",
]

[[package]]
name = "rle-decode-fast"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3582f63211428f83597b51b2ddb88e2a91a9d52d12831f9d08f5e624e8977422"

[[package]]
name = "rocksdb"
version = "0.3.0"
source = "git+https://github.com/tikv/rust-rocksdb.git#489433e1cbaba553500c5c9145bf7290f58d9e73"
dependencies = [
 "libc 0.2.174",
 "librocksdb_sys",
]

[[package]]
name = "rsa"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cf22754c49613d2b3b119f0e5d46e34a2c628a937e3024b8762de4e7d8c710b"
dependencies = [
 "byteorder",
 "digest 0.10.7",
 "num-bigint-dig",
 "num-integer",
 "num-iter",
 "num-traits",
 "pkcs1",
 "pkcs8",
 "rand_core 0.6.2",
 "smallvec",
 "subtle",
 "zeroize",
]

[[package]]
name = "rstest"
version = "0.18.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97eeab2f3c0a199bc4be135c36c924b6590b88c377d416494288c14f2db30199"
dependencies = [
 "futures 0.3.28",
 "futures-timer",
 "rstest_macros",
 "rustc_version 0.4.0",
]

[[package]]
name = "rstest_macros"
version = "0.18.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d428f8247852f894ee1be110b375111b586d4fa431f6c46e64ba5a0dcccbe605"
dependencies = [
 "cfg-if 1.0.0",
 "glob",
 "proc-macro2",
 "quote",
 "regex",
 "relative-path",
 "rustc_version 0.4.0",
 "syn 2.0.104",
 "unicode-ident",
]

[[package]]
name = "rusoto_core"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#d6ffce8898ce5d03ada531401437985b727393a4"
dependencies = [
 "async-trait",
 "base64 0.13.0",
 "bytes",
 "crc32fast",
 "futures 0.3.28",
 "http",
 "hyper",
 "hyper-tls",
 "lazy_static",
 "log",
 "rusoto_credential",
 "rusoto_signature",
 "rustc_version 0.3.3",
 "serde",
 "serde_json",
 "tokio",
 "xml-rs",
]

[[package]]
name = "rusoto_credential"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#d6ffce8898ce5d03ada531401437985b727393a4"
dependencies = [
 "async-trait",
 "chrono",
 "dirs-next 2.0.0",
 "futures 0.3.28",
 "hyper",
 "serde",
 "serde_json",
 "shlex 0.1.1",
 "tokio",
 "zeroize",
]

[[package]]
name = "rusoto_kms"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#d6ffce8898ce5d03ada531401437985b727393a4"
dependencies = [
 "async-trait",
 "bytes",
 "futures 0.3.28",
 "rusoto_core",
 "serde",
 "serde_json",
]

[[package]]
name = "rusoto_mock"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#d6ffce8898ce5d03ada531401437985b727393a4"
dependencies = [
 "async-trait",
 "chrono",
 "futures 0.3.28",
 "http",
 "rusoto_core",
 "serde",
 "serde_json",
]

[[package]]
name = "rusoto_s3"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#d6ffce8898ce5d03ada531401437985b727393a4"
dependencies = [
 "async-trait",
 "bytes",
 "futures 0.3.28",
 "rusoto_core",
 "serde",
 "serde_derive",
 "xml-rs",
]

[[package]]
name = "rusoto_signature"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#d6ffce8898ce5d03ada531401437985b727393a4"
dependencies = [
 "base64 0.13.0",
 "bytes",
 "chrono",
 "digest 0.9.0",
 "futures 0.3.28",
 "hex 0.4.3",
 "hmac",
 "http",
 "hyper",
 "log",
 "md-5",
 "percent-encoding",
 "pin-project-lite",
 "rusoto_credential",
 "rustc_version 0.3.3",
 "serde",
 "sha2 0.9.1",
 "tokio",
]

[[package]]
name = "rusoto_sts"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#d6ffce8898ce5d03ada531401437985b727393a4"
dependencies = [
 "async-trait",
 "bytes",
 "chrono",
 "futures 0.3.28",
 "rusoto_core",
 "serde_urlencoded",
 "xml-rs",
]

[[package]]
name = "rust-crypto"
version = "0.2.36"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f76d05d3993fd5f4af9434e8e436db163a12a9d40e1a58a726f27a01dfd12a2a"
dependencies = [
 "gcc",
 "libc 0.2.174",
 "rand 0.3.23",
 "rustc-serialize",
 "time 0.1.43",
]

[[package]]
name = "rust-ini"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c96a7d6722944454c68ff2ba2a252a4e9b0635c03dd510fdf482a2c8981cbf2"
dependencies = [
 "multimap",
]

[[package]]
name = "rust-stemmers"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e46a2036019fdb888131db7a4c847a1063a7493f971ed94ea82c67eada63ca54"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "rustc-demangle"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c691c0e608126e00913e33f0ccf3727d5fc84573623b8d65b2df340b5201783"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-serialize"
version = "0.3.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe834bc780604f4674073badbad26d7219cadfb4a2275802db12cbae17498401"

[[package]]
name = "rustc_version"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "138e3e0acb6c9fb258b19b67cb8abd63c00679d2851805ea151465464fe9030a"
dependencies = [
 "semver 0.9.0",
]

[[package]]
name = "rustc_version"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0dfe2087c51c460008730de8b57e6a320782fbfb312e1f4d520e6c6fae155ee"
dependencies = [
 "semver 0.11.0",
]

[[package]]
name = "rustc_version"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa0f585226d2e68097d4f95d113b15b83a82e819ab25717ec0590d9584ef366"
dependencies = [
 "semver 1.0.4",
]

[[package]]
name = "rustix"
version = "0.38.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdb5bc1ae2baa591800df16c9ca78619bf65c0488b41b96ccec5d11220d8c154"
dependencies = [
 "bitflags 2.5.0",
 "errno",
 "libc 0.2.174",
 "linux-raw-sys",
 "windows-sys 0.52.0",
]

[[package]]
name = "rustls"
version = "0.19.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35edb675feee39aec9c99fa5ff985081995a06d594114ae14cbe797ad7b7a6d7"
dependencies = [
 "base64 0.13.0",
 "log",
 "ring 0.16.20",
 "sct 0.6.1",
 "webpki",
]

[[package]]
name = "rustls"
version = "0.21.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fecbfb7b1444f477b345853b1fce097a2c6fb637b2bfb87e6bc5db0f043fae4"
dependencies = [
 "log",
 "ring 0.17.5",
 "rustls-webpki",
 "sct 0.7.0",
]

[[package]]
name = "rustls-native-certs"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9aace74cb666635c918e9c12bc0d348266037aa8eb599b5cba565709a8dff00"
dependencies = [
 "openssl-probe",
 "rustls-pemfile",
 "schannel",
 "security-framework",
]

[[package]]
name = "rustls-pemfile"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d3987094b1d07b653b7dfdc3f70ce9a1da9c51ac18c1b06b662e4f9a0e9f4b2"
dependencies = [
 "base64 0.21.2",
]

[[package]]
name = "rustls-webpki"
version = "0.101.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b6275d1ee7a1cd780b64aca7726599a1dbc893b1e64144529e55c3c2f745765"
dependencies = [
 "ring 0.17.5",
 "untrusted 0.9.0",
]

[[package]]
name = "rustversion"
version = "1.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f3208ce4d8448b3f3e7d168a73f5e0c43a61e32930de3bceeccedb388b6bf06"

[[package]]
name = "rusty-fork"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb3dcc6e454c328bb824492db107ab7c0ae8fcffe4ad210136ef014458c1bc4f"
dependencies = [
 "fnv",
 "quick-error",
 "tempfile",
 "wait-timeout",
]

[[package]]
name = "rustyline"
version = "6.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f0d5e7b0219a3eadd5439498525d4765c59b7c993ef0c12244865cd2d988413"
dependencies = [
 "cfg-if 0.1.10",
 "dirs-next 1.0.2",
 "libc 0.2.174",
 "log",
 "memchr",
 "nix 0.18.0",
 "scopeguard",
 "unicode-segmentation",
 "unicode-width",
 "utf8parse",
 "winapi 0.3.9",
]

[[package]]
name = "ryu"
version = "1.0.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3cb5ba0dc43242ce17de99c180e96db90b235b8a9fdc9543c96d2209116bd9f"

[[package]]
name = "safemem"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2b08423011dae9a5ca23f07cf57dac3857f5c885d352b76f6d95f4aea9434d0"

[[package]]
name = "same-file"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "585e8ddcedc187886a30fa705c47985c3fa88d06624095856b36ca0b82ff4421"
dependencies = [
 "winapi-util",
]

[[package]]
name = "schannel"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87f550b06b6cba9c8b8be3ee73f391990116bf527450d2556e9b9ce263b9a021"
dependencies = [
 "lazy_static",
 "winapi 0.3.9",
]

[[package]]
name = "schema"
version = "0.1.0"
dependencies = [
 "api_version",
 "async-trait",
 "base64 0.13.0",
 "bytes",
 "chrono",
 "kvenginepb",
 "log_wrappers",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "serde_repr",
 "slog",
 "slog-global",
 "slog-term",
 "tidb_query_datatype",
 "tikv_util",
 "tipb",
]

[[package]]
name = "schemars"
version = "0.8.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45a28f4c49489add4ce10783f7911893516f15afe45d015608d41faca6bc4d29"
dependencies = [
 "dyn-clone",
 "schemars_derive",
 "serde",
 "serde_json",
]

[[package]]
name = "schemars_derive"
version = "0.8.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c767fd6fa65d9ccf9cf026122c1b555f2ef9a4f0cea69da4d7dbc3e258d30967"
dependencies = [
 "proc-macro2",
 "quote",
 "serde_derive_internals",
 "syn 1.0.107",
]

[[package]]
name = "scopeguard"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d29ab0c6d3fc0ee92fe66e2d99f700eab17a8d57d1c1d3b748380fb20baa78cd"

[[package]]
name = "scratch"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3cf7c11c38cb994f3d40e8a8cde3bbd1f72a435e4c49e85d6553d8312306152"

[[package]]
name = "sct"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b362b83898e0e69f38515b82ee15aa80636befe47c3b6d3d89a911e78fc228ce"
dependencies = [
 "ring 0.16.20",
 "untrusted 0.7.1",
]

[[package]]
name = "sct"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d53dcdb7c9f8158937a7981b48accfd39a43af418591a5d008c7b22b5e1b7ca4"
dependencies = [
 "ring 0.16.20",
 "untrusted 0.7.1",
]

[[package]]
name = "seahash"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c107b6f4780854c8b126e228ea8869f4d7b71260f962fefb57b996b8959ba6b"

[[package]]
name = "secrecy"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9bd1c54ea06cfd2f6b63219704de0b9b4f72dcc2b8fdef820be6cd799780e91e"
dependencies = [
 "serde",
 "zeroize",
]

[[package]]
name = "security"
version = "0.0.1"
dependencies = [
 "aliyun",
 "aws",
 "base64 0.13.0",
 "bstr",
 "bytes",
 "cloud",
 "cloud_encryption",
 "collections",
 "encryption",
 "grpcio",
 "http",
 "hyper",
 "hyper-rustls",
 "kvproto",
 "rustls 0.21.11",
 "rustls-pemfile",
 "serde",
 "serde_derive",
 "serde_json",
 "slog",
 "slog-global",
 "tempfile",
 "thiserror",
 "tikv_util",
 "tokio",
]

[[package]]
name = "security-framework"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3670b1d2fdf6084d192bc71ead7aabe6c06aa2ea3fbd9cc3ac111fa5c2b1bd84"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation",
 "core-foundation-sys",
 "libc 0.2.174",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3676258fd3cfe2c9a0ec99ce3038798d847ce3e4bb17746373eb9f0f1ac16339"
dependencies = [
 "core-foundation-sys",
 "libc 0.2.174",
]

[[package]]
name = "seize"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4b8d813387d566f627f3ea1b914c068aac94c40ae27ec43f5f33bde65abefe7"
dependencies = [
 "libc 0.2.174",
 "windows-sys 0.52.0",
]

[[package]]
name = "semver"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d7eb9ef2c18661902cc47e535f9bc51b78acd254da71d375c2f6720d9a40403"
dependencies = [
 "semver-parser 0.7.0",
 "serde",
]

[[package]]
name = "semver"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "394cec28fa623e00903caf7ba4fa6fb9a0e260280bb8cdbbba029611108a0190"
dependencies = [
 "semver-parser 0.7.0",
]

[[package]]
name = "semver"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f301af10236f6df4160f7c3f04eec6dbc70ace82d23326abad5edee88801c6b6"
dependencies = [
 "semver-parser 0.10.2",
]

[[package]]
name = "semver"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "568a8e6258aa33c13358f81fd834adb854c6f7c9468520910a9b1e8fac068012"

[[package]]
name = "semver-parser"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "388a1df253eca08550bef6c72392cfe7c30914bf41df5269b68cbd6ff8f570a3"

[[package]]
name = "semver-parser"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0bef5b7f9e0df16536d3961cfb6e84331c065b4066afb39768d0e319411f7"
dependencies = [
 "pest",
]

[[package]]
name = "serde"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f0e2c6ed6606019b4e29e69dbaba95b11854410e5347d525002456dbbb786b6"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde-value"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3a1a3341211875ef120e117ea7fd5228530ae7e7036a779fdc9117be6b3282c"
dependencies = [
 "ordered-float 2.10.0",
 "serde",
]

[[package]]
name = "serde-xml-rs"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0bf1ba0696ccf0872866277143ff1fd14d22eec235d2b23702f95e6660f7dfa"
dependencies = [
 "log",
 "serde",
 "thiserror",
 "xml-rs",
]

[[package]]
name = "serde_cbor"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e18acfa2f90e8b735b2836ab8d538de304cbb6729a7360729ea5a895d15a622"
dependencies = [
 "half 1.8.2",
 "serde",
]

[[package]]
name = "serde_derive"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b0276cf7f2c73365f7157c8123c21cd9a50fbbd844757af28ca1f5925fc2a00"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "serde_derive_internals"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85bf8229e7920a9f636479437026331ce11aa132b4dde37d121944a44d6e5f3c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "serde_ignored"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c2c7d39d14f2f2ea82239de71594782f186fd03501ac81f0ce08e674819ff2f"
dependencies = [
 "serde",
]

[[package]]
name = "serde_json"
version = "1.0.140"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20068b6e96dc6c9bd23e01df8827e6c7e1f2fddd43c21810382803c136b99373"
dependencies = [
 "indexmap 2.10.0",
 "itoa 1.0.6",
 "memchr",
 "ryu",
 "serde",
]

[[package]]
name = "serde_path_to_error"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0421d4f173fab82d72d6babf36d57fae38b994ca5c2d78e704260ba6d12118b"
dependencies = [
 "serde",
]

[[package]]
name = "serde_repr"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a5ec9fa74a20ebbe5d9ac23dac1fc96ba0ecfe9f50f2843b52e537b10fbcb4e"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "serde_urlencoded"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edfa57a7f8d9c1d260a549e7224100f6c43d43f9103e06dd8b4095a9b2b43ce9"
dependencies = [
 "form_urlencoded",
 "itoa 0.4.8",
 "ryu",
 "serde",
]

[[package]]
name = "serde_with"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89d3d595d64120bbbc70b7f6d5ae63298b62a3d9f373ec2f56acf5365ca8a444"
dependencies = [
 "serde",
 "serde_with_macros",
]

[[package]]
name = "serde_with_macros"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4070d2c9b9d258465ad1d82aabb985b84cd9a3afa94da25ece5a9938ba5f1606"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "serde_yaml"
version = "0.9.34+deprecated"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a8b1a1a2ebf674015cc02edccce75287f1a0130d394307b36743c2f5d504b47"
dependencies = [
 "indexmap 2.10.0",
 "itoa 1.0.6",
 "ryu",
 "serde",
 "unsafe-libyaml",
]

[[package]]
name = "server"
version = "0.0.1"
dependencies = [
 "api_version",
 "backup",
 "backup-stream",
 "causal_ts",
 "cdc",
 "chrono",
 "clap 2.34.0",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption",
 "encryption_export",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_traits",
 "error_code",
 "file_system",
 "fs2",
 "futures 0.3.28",
 "grpcio",
 "grpcio-health",
 "hex 0.4.3",
 "keys",
 "kvproto",
 "libc 0.2.174",
 "log",
 "log_wrappers",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raft_log_engine",
 "raftstore",
 "rand 0.8.5",
 "resolved_ts",
 "resource_metering",
 "security",
 "serde_json",
 "signal-hook",
 "slog",
 "slog-global",
 "snap_recovery",
 "tempfile",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "toml",
 "txn_types",
 "yatp",
]

[[package]]
name = "sha-1"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5058ada175748e33390e40e872bd0fe59a19f265d0158daa551c5a88a76009c"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha1"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3bf829a2d51ab4a5ddf1352d8470c140cadc8301b2ae1789db023f01cedd6ba"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha2"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2933378ddfeda7ea26f48c555bdad8bb446bf8a3d17832dc83e380d444cfb8c1"
dependencies = [
 "block-buffer 0.9.0",
 "cfg-if 0.1.10",
 "cpuid-bool",
 "digest 0.9.0",
 "opaque-debug",
]

[[package]]
name = "sha2"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "793db75ad2bcafc3ffa7c68b215fee268f537982cd901d132f89c6343f3a3dc8"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "shlex"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fdf1b9db47230893d76faad238fd6097fd6d6a9245cd7a4d90dbd639536bbd2"

[[package]]
name = "shlex"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43b2853a4d09f215c24cc5489c992ce46052d359b5109343cbafbf26bc62f8a3"

[[package]]
name = "signal-hook"
version = "0.3.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a253b5e89e2698464fc26b545c9edceb338e18a89effeeecfea192c3025be29d"
dependencies = [
 "libc 0.2.174",
 "signal-hook-registry",
]

[[package]]
name = "signal-hook-registry"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e51e73328dc4ac0c7ccbda3a494dfa03df1de2f46018127f60c693f2648455b0"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "simsimd"
version = "6.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56838e1a0aceaef39fc1327227033efa84a294f54a39b356eefa5c60c885e9f0"
dependencies = [
 "cc",
]

[[package]]
name = "siphasher"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa8f3741c7372e75519bd9346068370c9cdaabcc1f9599cbcf2a2719352286b7"

[[package]]
name = "siphasher"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56199f7ddabf13fe5074ce809e7d3f42b42ae711800501b5b16ea82ad029c39d"
dependencies = [
 "serde",
]

[[package]]
name = "sketches-ddsketch"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85636c14b73d81f541e525f585c0a2109e6744e1565b5c1668e31c70c10ed65c"
dependencies = [
 "serde",
]

[[package]]
name = "slab"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6528351c9bc8ab22353f9d776db39a20288e8d6c37ef8cfe3317cf875eecfc2d"
dependencies = [
 "autocfg",
]

[[package]]
name = "slice-group-by"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "826167069c09b99d56f31e9ae5c99049e932a98c9dc2dac47645b08dbbf76ba7"

[[package]]
name = "slog"
version = "2.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cc9c640a4adbfbcc11ffb95efe5aa7af7309e002adab54b185507dbf2377b99"

[[package]]
name = "slog-async"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c60813879f820c85dbc4eabf3269befe374591289019775898d56a81a804fbdc"
dependencies = [
 "crossbeam-channel",
 "slog",
 "take_mut",
 "thread_local",
]

[[package]]
name = "slog-envlogger"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "906a1a0bc43fed692df4b82a5e2fbfc3733db8dad8bb514ab27a4f23ad04f5c0"
dependencies = [
 "log",
 "regex",
 "slog",
 "slog-async",
 "slog-scope",
 "slog-stdlog",
 "slog-term",
]

[[package]]
name = "slog-global"
version = "0.1.0"
source = "git+https://github.com/breeswish/slog-global.git?rev=d592f88e4dbba5eb439998463054f1a44fbf17b9#d592f88e4dbba5eb439998463054f1a44fbf17b9"
dependencies = [
 "arc-swap 0.4.8",
 "lazy_static",
 "log",
 "slog",
]

[[package]]
name = "slog-json"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ddc0d2aff1f8f325ef660d9a0eb6e6dcd20b30b3f581a5897f58bf42d061c37a"
dependencies = [
 "chrono",
 "serde",
 "serde_json",
 "slog",
]

[[package]]
name = "slog-scope"
version = "4.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f95a4b4c3274cd2869549da82b57ccc930859bdbf5bcea0424bc5f140b3c786"
dependencies = [
 "arc-swap 1.7.1",
 "lazy_static",
 "slog",
]

[[package]]
name = "slog-stdlog"
version = "4.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6706b2ace5bbae7291d3f8d2473e2bfab073ccd7d03670946197aec98471fa3e"
dependencies = [
 "log",
 "slog",
 "slog-scope",
]

[[package]]
name = "slog-term"
version = "2.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6e022d0b998abfe5c3782c1f03551a596269450ccd677ea51c56f8b214610e8"
dependencies = [
 "is-terminal",
 "slog",
 "term",
 "thread_local",
 "time 0.3.41",
]

[[package]]
name = "slog_derive"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a945ec7f7ce853e89ffa36be1e27dce9a43e82ff9093bf3461c30d5da74ed11b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "smallvec"
version = "1.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c5e1a9a646d36c3599cd173a41282daf47c44583ad367b8e6837255952e5c67"

[[package]]
name = "smartstring"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fb72c633efbaa2dd666986505016c32c3044395ceaf881518399d2f4127ee29"
dependencies = [
 "autocfg",
 "static_assertions",
 "version_check 0.9.4",
]

[[package]]
name = "snap_recovery"
version = "0.1.0"
dependencies = [
 "chrono",
 "encryption",
 "encryption_export",
 "engine_rocks",
 "engine_traits",
 "futures 0.3.28",
 "grpcio",
 "keys",
 "kvproto",
 "log",
 "pd_client",
 "protobuf",
 "raft_log_engine",
 "raftstore",
 "slog",
 "slog-global",
 "structopt",
 "tempfile",
 "thiserror",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "toml",
 "txn_types",
]

[[package]]
name = "snappy-sys"
version = "0.1.0"
source = "git+https://github.com/tikv/rust-snappy.git?branch=static-link#8c12738bad811397600455d6982aff754ea2ac44"
dependencies = [
 "cmake",
 "libc 0.2.174",
 "pkg-config",
]

[[package]]
name = "snmalloc-rs"
version = "0.2.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f5a6194d59b08fc87381e7c8a04ab4ab9967282b00f409bb742e08f3514ed0b"
dependencies = [
 "snmalloc-sys",
]

[[package]]
name = "snmalloc-sys"
version = "0.2.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9518813a25ab2704a6df4968f609aa6949705409b6a854dcc87018d12961cbc8"
dependencies = [
 "cmake",
]

[[package]]
name = "socket2"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02e2d2db9033d13a1567121ddd7a095ee144db4e1ca1b1bda3419bc0da294ebd"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "spin"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e63cff320ae2c57904679ba7cb63280a3dc4613885beafb148ee7bf9aa9042d"

[[package]]
name = "spin"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6980e8d7511241f8acf4aebddbb1ff938df5eebe98691418c4468d0b72a96a67"

[[package]]
name = "spki"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44d01ac02a6ccf3e07db148d2be087da624fea0221a16152ed01f0496a6b0a27"
dependencies = [
 "base64ct",
 "der",
]

[[package]]
name = "sqlformat"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4b7922be017ee70900be125523f38bdd644f4f06a1b16e8fa5a8ee8c34bffd4"
dependencies = [
 "itertools 0.10.5",
 "nom 7.1.3",
 "unicode_categories",
]

[[package]]
name = "sqlx"
version = "0.5.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "551873805652ba0d912fec5bbb0f8b4cdd96baf8e2ebf5970e5671092966019b"
dependencies = [
 "sqlx-core",
 "sqlx-macros",
]

[[package]]
name = "sqlx-core"
version = "0.5.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e48c61941ccf5ddcada342cd59e3e5173b007c509e1e8e990dafc830294d9dc5"
dependencies = [
 "ahash 0.7.8",
 "atoi",
 "bigdecimal",
 "bitflags 1.3.2",
 "byteorder",
 "bytes",
 "chrono",
 "crc",
 "crossbeam-queue",
 "digest 0.10.7",
 "either",
 "event-listener",
 "futures-channel",
 "futures-core",
 "futures-intrusive",
 "futures-util",
 "generic-array",
 "hashlink",
 "hex 0.4.3",
 "indexmap 1.6.2",
 "itoa 1.0.6",
 "libc 0.2.174",
 "log",
 "memchr",
 "num-bigint 0.3.3",
 "once_cell",
 "paste",
 "percent-encoding",
 "rand 0.8.5",
 "rsa",
 "rustls 0.19.1",
 "sha-1",
 "sha2 0.10.8",
 "smallvec",
 "sqlformat",
 "sqlx-rt",
 "stringprep",
 "thiserror",
 "tokio-stream",
 "url",
 "webpki",
 "webpki-roots",
]

[[package]]
name = "sqlx-macros"
version = "0.5.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc0fba2b0cae21fc00fe6046f8baa4c7fcb49e379f0f592b04696607f69ed2e1"
dependencies = [
 "dotenv",
 "either",
 "heck 0.4.0",
 "once_cell",
 "proc-macro2",
 "quote",
 "sha2 0.10.8",
 "sqlx-core",
 "sqlx-rt",
 "syn 1.0.107",
 "url",
]

[[package]]
name = "sqlx-rt"
version = "0.5.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4db708cd3e459078f85f39f96a00960bd841f66ee2a669e90bf36907f5a79aae"
dependencies = [
 "once_cell",
 "tokio",
 "tokio-rustls 0.22.0",
]

[[package]]
name = "sst_importer"
version = "0.1.0"
dependencies = [
 "api_version",
 "crc32fast",
 "dashmap 5.1.0",
 "encryption",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "external_storage_export",
 "file_system",
 "futures 0.3.28",
 "futures-util",
 "grpcio",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "openssl",
 "prometheus",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tempfile",
 "test_sst_importer",
 "test_util",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "txn_types",
 "uuid 0.8.2",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "str-buf"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d44a3643b4ff9caf57abcee9c2c621d6c03d9135e0d8b589bd9afb5992cb176a"

[[package]]
name = "str_stack"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9091b6114800a5f2141aee1d1b9d6ca3592ac062dc5decb3764ec5895a47b4eb"

[[package]]
name = "stringprep"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb41d74e231a107a1b4ee36bd1214b11285b77768d2e3824aedafa988fd36ee6"
dependencies = [
 "finl_unicode",
 "unicode-bidi",
 "unicode-normalization",
]

[[package]]
name = "strsim"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ea5119cdb4c55b55d432abb513a0429384878c15dde60cc77b1c99de1a95a6a"

[[package]]
name = "strsim"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "032c03039aae92b350aad2e3779c352e104d919cb192ba2fabbd7b831ce4f0f6"

[[package]]
name = "strsim"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73473c0e59e6d5812c5dfe2a064a6444949f089e20eec9a2e5506596494e4623"

[[package]]
name = "structopt"
version = "0.3.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "126d630294ec449fae0b16f964e35bf3c74f940da9dca17ee9b905f7b3112eb8"
dependencies = [
 "clap 2.34.0",
 "lazy_static",
 "structopt-derive",
]

[[package]]
name = "structopt-derive"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65e51c492f9e23a220534971ff5afc14037289de430e3c83f9daf6a1b6ae91e8"
dependencies = [
 "heck 0.3.1",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "strum"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7318c509b5ba57f18533982607f24070a55d353e90d4cae30c467cdb2ad5ac5c"
dependencies = [
 "strum_macros 0.20.1",
]

[[package]]
name = "strum"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "063e6045c0e62079840579a7e47a355ae92f60eb74daaf156fb1e84ba164e63f"
dependencies = [
 "strum_macros 0.24.3",
]

[[package]]
name = "strum_macros"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee8bc6b87a5112aeeab1f4a9f7ab634fe6cbefc4850006df31267f4cfb9e3149"
dependencies = [
 "heck 0.3.1",
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "strum_macros"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e385be0d24f186b4ce2f9982191e7101bb737312ad61c1f2f984f34bcf85d59"
dependencies = [
 "heck 0.4.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 1.0.107",
]

[[package]]
name = "subtle"
version = "2.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13c2bddecc57b384dee18652358fb23172facb8a2c51ccc10d74c157bdea3292"

[[package]]
name = "symbolic-common"
version = "12.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26212dc7aeb75abb4cc84320a50dd482977089402f7b4043b454d6d79d8536e7"
dependencies = [
 "debugid",
 "memmap2 0.5.3",
 "stable_deref_trait",
 "uuid 1.2.2",
]

[[package]]
name = "symbolic-demangle"
version = "12.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76f1b0155f588568b2df0d693b30aeedb59360b647b85fc3c23942e81e8cc97a"
dependencies = [
 "rustc-demangle",
 "symbolic-common",
]

[[package]]
name = "syn"
version = "1.0.107"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f4064b5b16e03ae50984a5a8ed5d4f8803e6bc1fd170a3cda91a1be4b18e3f5"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.104"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17b6f705963418cdb9927482fa304bc562ece2fdd4f616084c50b7023b435a40"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "sync_wrapper"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20518fe4a4c9acf048008599e464deb21beeae3d3578418951a189c235a7a9a8"

[[package]]
name = "sysinfo"
version = "0.16.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c280c91abd1aed2e36be1bc8f56fbc7a2acbb2b58fbcac9641510179cc72dd9"
dependencies = [
 "cfg-if 1.0.0",
 "core-foundation-sys",
 "doc-comment",
 "libc 0.2.174",
 "ntapi 0.3.3",
 "once_cell",
 "rayon",
 "winapi 0.3.9",
]

[[package]]
name = "sysinfo"
version = "0.26.9"
source = "git+https://github.com/tikv/sysinfo?branch=0.26-fix-cpu#5a1bcf08816979624ef2ad79cfb896de432a9501"
dependencies = [
 "cfg-if 1.0.0",
 "core-foundation-sys",
 "libc 0.2.174",
 "ntapi 0.4.1",
 "once_cell",
 "rayon",
 "winapi 0.3.9",
]

[[package]]
name = "take_mut"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f764005d11ee5f36500a149ace24e00e3da98b0158b3e2d53a7495660d3f4d60"

[[package]]
name = "tame-gcs"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d20ec2d6525a66afebdff9e1d8ef143c9deae9a3b040c61d3cfa9ae6fda80060"
dependencies = [
 "base64 0.13.0",
 "bytes",
 "chrono",
 "futures-util",
 "http",
 "percent-encoding",
 "pin-utils",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "thiserror",
 "url",
]

[[package]]
name = "tame-oauth"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9435c9348e480fad0f2215d5602e2dfad03df8a6398c4e7ceaeaa42758f26a8a"
dependencies = [
 "base64 0.13.0",
 "chrono",
 "http",
 "lock_api",
 "parking_lot 0.11.1",
 "ring 0.16.20",
 "serde",
 "serde_json",
 "twox-hash",
 "url",
]

[[package]]
name = "tantivy"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8d0582f186c0a6d55655d24543f15e43607299425c5ad8352c242b914b31856"
dependencies = [
 "aho-corasick",
 "arc-swap 1.7.1",
 "base64 0.22.1",
 "bitpacking",
 "byteorder",
 "census",
 "crc32fast",
 "crossbeam-channel",
 "downcast-rs",
 "fastdivide",
 "fnv",
 "fs4",
 "htmlescape",
 "itertools 0.12.1",
 "levenshtein_automata",
 "log",
 "lru",
 "lz4_flex",
 "measure_time",
 "memmap2 0.9.5",
 "num_cpus",
 "once_cell",
 "oneshot",
 "rayon",
 "regex",
 "rust-stemmers",
 "rustc-hash",
 "serde",
 "serde_json",
 "sketches-ddsketch",
 "smallvec",
 "tantivy-bitpacker",
 "tantivy-columnar",
 "tantivy-common",
 "tantivy-fst",
 "tantivy-query-grammar",
 "tantivy-stacker",
 "tantivy-tokenizer-api",
 "tempfile",
 "thiserror",
 "time 0.3.41",
 "uuid 1.2.2",
 "winapi 0.3.9",
]

[[package]]
name = "tantivy-bitpacker"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "284899c2325d6832203ac6ff5891b297fc5239c3dc754c5bc1977855b23c10df"
dependencies = [
 "bitpacking",
]

[[package]]
name = "tantivy-columnar"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12722224ffbe346c7fec3275c699e508fd0d4710e629e933d5736ec524a1f44e"
dependencies = [
 "downcast-rs",
 "fastdivide",
 "itertools 0.12.1",
 "serde",
 "tantivy-bitpacker",
 "tantivy-common",
 "tantivy-sstable",
 "tantivy-stacker",
]

[[package]]
name = "tantivy-common"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8019e3cabcfd20a1380b491e13ff42f57bb38bf97c3d5fa5c07e50816e0621f4"
dependencies = [
 "async-trait",
 "byteorder",
 "ownedbytes",
 "serde",
 "time 0.3.41",
]

[[package]]
name = "tantivy-fst"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d60769b80ad7953d8a7b2c70cdfe722bbcdcac6bccc8ac934c40c034d866fc18"
dependencies = [
 "byteorder",
 "regex-syntax",
 "utf8-ranges",
]

[[package]]
name = "tantivy-query-grammar"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "847434d4af57b32e309f4ab1b4f1707a6c566656264caa427ff4285c4d9d0b82"
dependencies = [
 "nom 7.1.3",
]

[[package]]
name = "tantivy-sstable"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c69578242e8e9fc989119f522ba5b49a38ac20f576fc778035b96cc94f41f98e"
dependencies = [
 "tantivy-bitpacker",
 "tantivy-common",
 "tantivy-fst",
 "zstd 0.13.3",
]

[[package]]
name = "tantivy-stacker"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c56d6ff5591fc332739b3ce7035b57995a3ce29a93ffd6012660e0949c956ea8"
dependencies = [
 "murmurhash32",
 "rand_distr",
 "tantivy-common",
]

[[package]]
name = "tantivy-tokenizer-api"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a0dcade25819a89cfe6f17d932c9cedff11989936bf6dd4f336d50392053b04"
dependencies = [
 "serde",
]

[[package]]
name = "tcmalloc"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "375205113d84a1c5eeed67beaa0ce08e41be1a9d5acc3425ad2381fddd9d819b"
dependencies = [
 "tcmalloc-sys",
]

[[package]]
name = "tcmalloc-sys"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b7ad73e635dd232c2c2106d59269f59a61de421cc6b95252d2d932094ff1f40"

[[package]]
name = "tempdir"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15f2b5fb00ccdf689e0149d1b1b3c03fead81c2b37735d812fa8bddbbf41b6d8"
dependencies = [
 "rand 0.4.6",
 "remove_dir_all",
]

[[package]]
name = "tempfile"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5cdb1ef4eaeeaddc8fbd371e5017057064af0911902ef36b39801f67cc6d79e4"
dependencies = [
 "cfg-if 1.0.0",
 "fastrand 1.8.0",
 "libc 0.2.174",
 "redox_syscall 0.2.11",
 "remove_dir_all",
 "winapi 0.3.9",
]

[[package]]
name = "term"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c59df8ac95d96ff9bede18eb7300b0fda5e5d8d90960e76f8e14ae765eedbf1f"
dependencies = [
 "dirs-next 2.0.0",
 "rustversion",
 "winapi 0.3.9",
]

[[package]]
name = "termcolor"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bab24d30b911b2376f3a13cc2cd443142f0c81dda04c118693e35b3835757755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "test_backup"
version = "0.0.1"
dependencies = [
 "api_version",
 "backup",
 "collections",
 "concurrency_manager",
 "crc64fast",
 "engine_traits",
 "external_storage_export",
 "file_system",
 "futures 0.3.28",
 "futures-executor",
 "futures-util",
 "grpcio",
 "kvproto",
 "protobuf",
 "rand 0.8.5",
 "tempfile",
 "test_raftstore",
 "tidb_query_common",
 "tikv",
 "tikv_util",
 "txn_types",
]

[[package]]
name = "test_cloud_server"
version = "0.1.0"
dependencies = [
 "anyhow",
 "api_version",
 "async-stream 0.2.0",
 "bstr",
 "builtin_dfs",
 "bytes",
 "chrono",
 "cloud_server",
 "cloud_worker",
 "codec",
 "collections",
 "crc32c",
 "crc32fast",
 "dashmap 4.0.2",
 "engine_traits",
 "fail 0.5.0",
 "file_system",
 "futures 0.3.28",
 "glob",
 "grpcio",
 "hex 0.4.3",
 "hyper",
 "kvengine",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "load_data",
 "log_wrappers",
 "nix 0.24.3",
 "parking_lot 0.12.0",
 "pd_client",
 "protobuf",
 "quick-xml 0.23.1",
 "rand 0.8.5",
 "regex",
 "reqwest",
 "rfengine",
 "rfstore",
 "rstest",
 "schema",
 "security",
 "serde",
 "serde_derive",
 "serde_json",
 "slog",
 "slog-global",
 "tempfile",
 "test_pd",
 "test_pd_client",
 "test_raftstore",
 "test_util",
 "thiserror",
 "tidb_query_common",
 "tidb_query_datatype",
 "tikv",
 "tikv-client",
 "tikv_alloc",
 "tikv_util",
 "tipb",
 "tokio",
 "tokio-util",
 "toml",
 "url",
 "zipf 7.0.0",
]

[[package]]
name = "test_coprocessor"
version = "0.0.1"
dependencies = [
 "api_version",
 "collections",
 "concurrency_manager",
 "engine_rocks",
 "futures 0.3.28",
 "kvproto",
 "protobuf",
 "resource_metering",
 "security",
 "test_storage",
 "tidb_query_common",
 "tidb_query_datatype",
 "tikv",
 "tikv_util",
 "tipb",
 "txn_types",
]

[[package]]
name = "test_pd"
version = "0.0.1"
dependencies = [
 "collections",
 "fail 0.5.0",
 "futures 0.3.28",
 "grpcio",
 "kvproto",
 "pd_client",
 "security",
 "slog",
 "slog-global",
 "tikv_util",
]

[[package]]
name = "test_pd_client"
version = "0.0.1"
dependencies = [
 "api_version",
 "cloud_encryption",
 "collections",
 "fail 0.5.0",
 "futures 0.3.28",
 "grpcio",
 "keys",
 "kvproto",
 "log_wrappers",
 "pd_client",
 "raft",
 "security",
 "slog",
 "slog-global",
 "test_pd",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
 "url",
]

[[package]]
name = "test_raftstore"
version = "0.0.1"
dependencies = [
 "api_version",
 "backtrace",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption_export",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_test",
 "engine_traits",
 "fail 0.5.0",
 "file_system",
 "futures 0.3.28",
 "grpcio",
 "grpcio-health",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "pd_client",
 "protobuf",
 "raft",
 "raftstore",
 "rand 0.8.5",
 "resolved_ts",
 "resource_metering",
 "security",
 "server",
 "slog",
 "slog-global",
 "tempfile",
 "test_pd_client",
 "test_util",
 "tikv",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
]

[[package]]
name = "test_sst_importer"
version = "0.1.0"
dependencies = [
 "crc32fast",
 "engine_rocks",
 "engine_traits",
 "keys",
 "kvproto",
 "uuid 0.8.2",
]

[[package]]
name = "test_storage"
version = "0.0.1"
dependencies = [
 "api_version",
 "collections",
 "futures 0.3.28",
 "kvproto",
 "pd_client",
 "raftstore",
 "test_raftstore",
 "tikv",
 "tikv_util",
 "tracker",
 "txn_types",
]

[[package]]
name = "test_util"
version = "0.0.1"
dependencies = [
 "backtrace",
 "collections",
 "encryption_export",
 "fail 0.5.0",
 "grpcio",
 "kvproto",
 "log",
 "rand 0.8.5",
 "rand_isaac",
 "security",
 "slog",
 "slog-global",
 "tempfile",
 "tikv_util",
 "time 0.1.43",
]

[[package]]
name = "tests"
version = "0.0.1"
dependencies = [
 "anyhow",
 "api_version",
 "arrow",
 "async-trait",
 "batch-system",
 "bincode",
 "byteorder",
 "bytes",
 "causal_ts",
 "cdc",
 "chrono",
 "cloud_encryption",
 "cloud_server",
 "cloud_worker",
 "codec",
 "collections",
 "concurrency_manager",
 "crc64fast",
 "criterion",
 "criterion-cpu-time",
 "criterion-perf-events",
 "crossbeam",
 "dashmap 4.0.2",
 "encryption",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_test",
 "engine_traits",
 "error_code",
 "external_storage_export",
 "fail 0.5.0",
 "file_system",
 "futures 0.3.28",
 "grpcio",
 "grpcio-health",
 "hex 0.4.3",
 "http",
 "hyper",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "load_data",
 "log",
 "log_wrappers",
 "merged_engine",
 "more-asserts",
 "native_br",
 "online_config",
 "panic_hook",
 "paste",
 "pd_client",
 "perfcnt",
 "procinfo",
 "profiler",
 "proptest",
 "protobuf",
 "raft",
 "raft_log_engine",
 "raftstore",
 "rand 0.8.5",
 "rand_xorshift",
 "replication_worker",
 "resource_metering",
 "rfengine",
 "rfenginepb",
 "rfstore",
 "rstest",
 "schema",
 "security",
 "serde",
 "serde_derive",
 "serde_json",
 "slog",
 "slog-global",
 "sqlx",
 "sst_importer",
 "tempfile",
 "test_backup",
 "test_cloud_server",
 "test_coprocessor",
 "test_pd",
 "test_pd_client",
 "test_raftstore",
 "test_sst_importer",
 "test_storage",
 "test_util",
 "tidb_query_aggr",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_executors",
 "tidb_query_expr",
 "tikv",
 "tikv-client",
 "tikv_kv",
 "tikv_util",
 "time 0.1.43",
 "tipb",
 "tipb_helper",
 "tokio",
 "toml",
 "txn_types",
 "uuid 0.8.2",
]

[[package]]
name = "textwrap"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d326610f408c7a4eb6f51c37c330e496b08506c9457c9d34287ecc38809fb060"
dependencies = [
 "unicode-width",
]

[[package]]
name = "textwrap"
version = "0.15.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "949517c0cf1bf4ee812e2e07e08ab448e3ae0d23472aee8a06c985f0c8815b16"

[[package]]
name = "thiserror"
version = "1.0.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "611040a08a0439f8248d1990b111c95baa9c704c805fa1f62104b39655fd7f90"
dependencies = [
 "thiserror-impl",
]

[[package]]
name = "thiserror-impl"
version = "1.0.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "090198534930841fab3a5d1bb637cde49e339654e606195f8d9c76eeb081dc96"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "thread_local"
version = "1.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5516c27b78311c50bf42c071425c560ac799b11c30b31f87e3081965fe5e0180"
dependencies = [
 "once_cell",
]

[[package]]
name = "tidb_query_aggr"
version = "0.0.1"
dependencies = [
 "match-template",
 "panic_hook",
 "tidb_query_codegen",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_expr",
 "tikv_util",
 "tipb",
 "tipb_helper",
]

[[package]]
name = "tidb_query_codegen"
version = "0.0.1"
dependencies = [
 "darling 0.10.1",
 "heck 0.3.1",
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "tidb_query_common"
version = "0.0.1"
dependencies = [
 "anyhow",
 "api_version",
 "async-trait",
 "byteorder",
 "derive_more",
 "error_code",
 "futures 0.3.28",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "maybe-async",
 "paste",
 "prometheus",
 "prometheus-static-metric",
 "serde_json",
 "thiserror",
 "tikv_util",
 "time 0.1.43",
 "yatp",
]

[[package]]
name = "tidb_query_datatype"
version = "0.0.1"
dependencies = [
 "api_version",
 "base64 0.13.0",
 "bitfield",
 "bitflags 1.3.2",
 "boolinator",
 "bstr",
 "bytemuck",
 "chrono",
 "chrono-tz",
 "codec",
 "collections",
 "criterion",
 "encoding_rs 0.8.29",
 "error_code",
 "hex 0.4.3",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "match-template",
 "nom 7.1.3",
 "num 0.3.0",
 "num-derive",
 "num-traits",
 "ordered-float 2.10.0",
 "protobuf",
 "regex",
 "serde",
 "serde_json",
 "simsimd",
 "slog",
 "slog-global",
 "static_assertions",
 "thiserror",
 "tidb_query_common",
 "tikv_alloc",
 "tikv_util",
 "tipb",
]

[[package]]
name = "tidb_query_executors"
version = "0.0.1"
dependencies = [
 "anyhow",
 "api_version",
 "async-trait",
 "bytes",
 "codec",
 "collections",
 "fail 0.5.0",
 "futures 0.3.28",
 "itertools 0.10.5",
 "kvengine",
 "kvproto",
 "log_wrappers",
 "match-template",
 "protobuf",
 "slog",
 "slog-global",
 "smallvec",
 "tidb_query_aggr",
 "tidb_query_codegen",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_expr",
 "tikv_util",
 "tipb",
 "tipb_helper",
 "txn_types",
 "yatp",
]

[[package]]
name = "tidb_query_expr"
version = "0.0.1"
dependencies = [
 "base64 0.13.0",
 "bstr",
 "byteorder",
 "chrono",
 "codec",
 "file_system",
 "flate2",
 "hex 0.4.3",
 "log_wrappers",
 "match-template",
 "num 0.3.0",
 "num-traits",
 "openssl",
 "panic_hook",
 "profiler",
 "protobuf",
 "rand 0.8.5",
 "regex",
 "safemem",
 "serde",
 "serde_json",
 "static_assertions",
 "tidb_query_codegen",
 "tidb_query_common",
 "tidb_query_datatype",
 "tikv_util",
 "time 0.1.43",
 "tipb",
 "tipb_helper",
 "twoway",
 "uuid 0.8.2",
]

[[package]]
name = "tikv"
version = "7.5.0"
dependencies = [
 "anyhow",
 "api_version",
 "async-stream 0.2.0",
 "async-trait",
 "backtrace",
 "batch-system",
 "bincode",
 "byteorder",
 "bytes",
 "case_macros",
 "causal_ts",
 "chrono",
 "codec",
 "collections",
 "concurrency_manager",
 "coprocessor_plugin_api",
 "crc32fast",
 "crc64fast",
 "crossbeam",
 "dashmap 5.1.0",
 "encryption_export",
 "engine_panic",
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "engine_traits_tests",
 "error_code",
 "example_coprocessor_plugin",
 "fail 0.5.0",
 "farmhash",
 "file_system",
 "flate2",
 "futures 0.3.28",
 "futures-executor",
 "futures-timer",
 "futures-util",
 "fxhash",
 "getset",
 "grpcio",
 "grpcio-health",
 "hex 0.4.3",
 "http",
 "hyper",
 "hyper-openssl",
 "hyper-rustls",
 "hyper-tls",
 "into_other",
 "itertools 0.10.5",
 "keyed_priority_queue",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "libloading",
 "log",
 "log_wrappers",
 "match-template",
 "maybe-async",
 "memory_trace_macros",
 "mime",
 "more-asserts",
 "mur3",
 "nom 5.1.3",
 "notify",
 "num-traits",
 "num_cpus",
 "online_config",
 "openssl",
 "overload_protector",
 "panic_hook",
 "parking_lot 0.12.0",
 "paste",
 "pd_client",
 "pin-project",
 "pnet_datalink",
 "pprof",
 "procinfo",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "quick_cache",
 "raft",
 "raft_log_engine",
 "raftstore",
 "rand 0.7.3",
 "recovery",
 "regex",
 "reqwest",
 "resource_metering",
 "rev_lines",
 "rfengine",
 "rfstore",
 "rstest",
 "seahash",
 "security",
 "semver 0.11.0",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "slog",
 "slog-global",
 "smallvec",
 "sst_importer",
 "strum 0.20.0",
 "sync_wrapper",
 "sysinfo 0.16.4",
 "tempfile",
 "test_sst_importer",
 "test_util",
 "thiserror",
 "tidb_query_aggr",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_executors",
 "tidb_query_expr",
 "tikv_alloc",
 "tikv_kv",
 "tikv_util",
 "time 0.1.43",
 "tipb",
 "tokio",
 "tokio-openssl",
 "tokio-timer",
 "toml",
 "tracker",
 "txn_types",
 "url",
 "uuid 0.8.2",
 "walkdir",
 "yatp",
 "zipf 6.1.0",
]

[[package]]
name = "tikv-client"
version = "0.3.0"
source = "git+https://github.com/tikv/client-rust.git?branch=cse#c7db27f8b4f7001e54d4d903bb102d4189b5ec82"
dependencies = [
 "async-recursion 0.3.2",
 "async-trait",
 "derive-new",
 "either",
 "fail 0.4.0",
 "futures 0.3.28",
 "lazy_static",
 "log",
 "pin-project",
 "prometheus",
 "prost 0.12.1",
 "rand 0.8.5",
 "regex",
 "semver 1.0.4",
 "serde",
 "serde_derive",
 "thiserror",
 "tokio",
 "tonic 0.10.2",
]

[[package]]
name = "tikv-ctl"
version = "0.0.1"
dependencies = [
 "backup",
 "cc",
 "cdc",
 "chrono",
 "clap 2.34.0",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption_export",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "file_system",
 "futures 0.3.28",
 "gag",
 "grpcio",
 "hex 0.4.3",
 "keys",
 "kvproto",
 "libc 0.2.174",
 "log",
 "log_wrappers",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raft-engine-ctl",
 "raft_log_engine",
 "raftstore",
 "rand 0.8.5",
 "regex",
 "security",
 "serde_json",
 "server",
 "signal-hook",
 "slog",
 "slog-global",
 "structopt",
 "tempfile",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "time 0.1.43",
 "tokio",
 "toml",
 "txn_types",
]

[[package]]
name = "tikv-jemalloc-ctl"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e37706572f4b151dff7a0146e040804e9c26fe3a3118591112f05cf12a4216c1"
dependencies = [
 "libc 0.2.174",
 "paste",
 "tikv-jemalloc-sys",
]

[[package]]
name = "tikv-jemalloc-sys"
version = "0.5.0+5.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aeab4310214fe0226df8bfeb893a291a58b19682e8a07e1e1d4483ad4200d315"
dependencies = [
 "cc",
 "fs_extra",
 "libc 0.2.174",
]

[[package]]
name = "tikv-jemallocator"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20612db8a13a6c06d57ec83953694185a367e16945f66565e8028d2c0bd76979"
dependencies = [
 "libc 0.2.174",
 "tikv-jemalloc-sys",
]

[[package]]
name = "tikv-server"
version = "0.0.1"
dependencies = [
 "cc",
 "clap 2.34.0",
 "cloud_server",
 "serde_json",
 "server",
 "tikv",
 "time 0.1.43",
 "toml",
]

[[package]]
name = "tikv-worker"
version = "0.1.0"
dependencies = [
 "chrono",
 "clap 2.34.0",
 "cloud_worker",
 "grpcio",
 "lazy_static",
 "pd_client",
 "prometheus",
 "security",
 "slog",
 "slog-global",
 "slog-term",
 "tikv",
 "tikv_util",
 "time 0.1.43",
 "toml",
]

[[package]]
name = "tikv_alloc"
version = "0.1.0"
dependencies = [
 "fxhash",
 "lazy_static",
 "libc 0.2.174",
 "mimalloc",
 "snmalloc-rs",
 "tcmalloc",
 "tempfile",
 "tikv-jemalloc-ctl",
 "tikv-jemalloc-sys",
 "tikv-jemallocator",
]

[[package]]
name = "tikv_kv"
version = "0.1.0"
dependencies = [
 "backtrace",
 "collections",
 "concurrency_manager",
 "engine_panic",
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "error_code",
 "fail 0.5.0",
 "file_system",
 "futures 0.3.28",
 "into_other",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "log_wrappers",
 "panic_hook",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "raft",
 "raftstore",
 "rfstore",
 "slog",
 "slog-global",
 "slog_derive",
 "tempfile",
 "thiserror",
 "tikv_util",
 "tracker",
 "txn_types",
]

[[package]]
name = "tikv_util"
version = "0.1.0"
dependencies = [
 "async-speed-limit",
 "backtrace",
 "byteorder",
 "bytes",
 "chrono",
 "codec",
 "collections",
 "cpu-time",
 "crc32fast",
 "crossbeam",
 "derive_more",
 "error_code",
 "fail 0.5.0",
 "fs2",
 "futures 0.3.28",
 "futures-util",
 "gag",
 "grpcio",
 "http",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "log",
 "log_wrappers",
 "mnt",
 "nix 0.24.3",
 "num-traits",
 "num_cpus",
 "online_config",
 "openssl",
 "page_size",
 "panic_hook",
 "pin-project",
 "procfs",
 "procinfo",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "rand 0.8.5",
 "regex",
 "rusoto_core",
 "serde",
 "serde_json",
 "slog",
 "slog-async",
 "slog-envlogger",
 "slog-global",
 "slog-json",
 "slog-term",
 "sysinfo 0.26.9",
 "tempfile",
 "thiserror",
 "tikv_alloc",
 "time 0.1.43",
 "tokio",
 "tokio-executor",
 "tokio-timer",
 "toml",
 "tracker",
 "url",
 "utime",
 "yatp",
]

[[package]]
name = "time"
version = "0.1.43"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca8a50ef2360fbd1eeb0ecd46795a87a19024eb4b53c5dc916ca1fd95fe62438"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "time"
version = "0.3.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a7619e19bc266e0f9c5e6686659d394bc57973859340060a69221e57dbc0c40"
dependencies = [
 "deranged",
 "itoa 1.0.6",
 "num-conv",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9e9a38711f559d9e3ce1cdb06dd7c5b8ea546bc90052da6d06bb76da74bb07c"

[[package]]
name = "time-macros"
version = "0.2.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3526739392ec93fd8b359c8e98514cb3e8e021beb4e5f597b00a0221f8ed8a49"
dependencies = [
 "num-conv",
 "time-core",
]

[[package]]
name = "tinytemplate"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2ada8616fad06a2d0c455adc530de4ef57605a8120cc65da9653e0e9623ca74"
dependencies = [
 "serde",
 "serde_json",
]

[[package]]
name = "tinyvec"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09b3661f17e86524eccd4371ab0429194e0d7c008abb45f7a7495b1719463c71"
dependencies = [
 "tinyvec_macros",
]

[[package]]
name = "tinyvec_macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3ccbac311fea05f86f61904b462b55fb3df8837a366dfc601a0161d0532f20"

[[package]]
name = "tipb"
version = "0.0.1"
source = "git+https://github.com/pingcap/tipb.git#0983782ed9834f5e8d4b8197e4036e462ca75e47"
dependencies = [
 "futures 0.3.28",
 "grpcio",
 "protobuf",
 "protobuf-build",
]

[[package]]
name = "tipb_helper"
version = "0.0.1"
dependencies = [
 "codec",
 "tidb_query_datatype",
 "tipb",
]

[[package]]
name = "tokio"
version = "1.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "597a12a59981d9e3c38d216785b0c37399f6e415e8d0712047620f189371b0bb"
dependencies = [
 "autocfg",
 "bytes",
 "libc 0.2.174",
 "memchr",
 "mio 0.8.5",
 "num_cpus",
 "parking_lot 0.12.0",
 "pin-project-lite",
 "signal-hook-registry",
 "socket2",
 "tokio-macros",
 "windows-sys 0.42.0",
]

[[package]]
name = "tokio-executor"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb2d1b8f4548dbf5e1f7818512e9c406860678f29c300cdf0ebac72d1a3a1671"
dependencies = [
 "crossbeam-utils 0.7.2",
 "futures 0.1.31",
]

[[package]]
name = "tokio-io-timeout"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30b74022ada614a1b4834de765f9bb43877f910cc8ce4be40e89042c9223a8bf"
dependencies = [
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-macros"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b557f72f448c511a979e2564e55d74e6c4432fc96ff4f6241bc6bded342643b7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "tokio-native-tls"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7d995660bd2b7f8c1568414c1126076c13fbb725c40112dc0120b78eb9b717b"
dependencies = [
 "native-tls",
 "tokio",
]

[[package]]
name = "tokio-openssl"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac1bec5c0a4aa71e3459802c7a12e8912c2091ce2151004f9ce95cc5d1c6124e"
dependencies = [
 "futures 0.3.28",
 "openssl",
 "pin-project",
 "tokio",
]

[[package]]
name = "tokio-rustls"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc6844de72e57df1980054b38be3a9f4702aba4858be64dd700181a8a6d0e1b6"
dependencies = [
 "rustls 0.19.1",
 "tokio",
 "webpki",
]

[[package]]
name = "tokio-rustls"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c28327cf380ac148141087fbfb9de9d7bd4e84ab5d2c28fbc911d753de8a7081"
dependencies = [
 "rustls 0.21.11",
 "tokio",
]

[[package]]
name = "tokio-stream"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d660770404473ccd7bc9f8b28494a811bc18542b915c0855c51e8f419d5223ce"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-timer"
version = "0.2.13"
source = "git+https://github.com/tikv/tokio?branch=tokio-timer-hotfix#e8ac149d93f4a9bf49ea569d8d313ee40c5eb448"
dependencies = [
 "crossbeam-utils 0.7.2",
 "futures 0.1.31",
 "slab",
 "tokio-executor",
]

[[package]]
name = "tokio-tungstenite"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec509ac96e9a0c43427c74f003127d953a265737636129424288d27cb5c4b12c"
dependencies = [
 "futures-util",
 "log",
 "tokio",
 "tungstenite",
]

[[package]]
name = "tokio-util"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "806fe8c2c87eccc8b3267cbae29ed3ab2d0bd37fca70ab622e46aaa9375ddb7d"
dependencies = [
 "bytes",
 "futures-core",
 "futures-io",
 "futures-sink",
 "pin-project-lite",
 "slab",
 "tokio",
 "tracing",
]

[[package]]
name = "toml"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75cf45bb0bef80604d001caaec0d09da99611b3c0fd39d3080468875cdb65645"
dependencies = [
 "serde",
]

[[package]]
name = "tonic"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55b9af819e54b8f33d453655bef9b9acc171568fb49523078d0cc4e7484200ec"
dependencies = [
 "async-stream 0.3.3",
 "async-trait",
 "axum 0.5.17",
 "base64 0.13.0",
 "bytes",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "hyper",
 "hyper-timeout",
 "percent-encoding",
 "pin-project",
 "prost 0.11.9",
 "prost-derive 0.11.9",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tower",
 "tower-layer",
 "tower-service",
 "tracing",
 "tracing-futures",
]

[[package]]
name = "tonic"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3082666a3a6433f7f511c7192923fa1fe07c69332d3c6a2e6bb040b569199d5a"
dependencies = [
 "async-trait",
 "axum 0.6.18",
 "base64 0.21.2",
 "bytes",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "hyper",
 "hyper-timeout",
 "percent-encoding",
 "pin-project",
 "prost 0.11.9",
 "tokio",
 "tokio-stream",
 "tower",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tonic"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d560933a0de61cf715926b9cac824d4c883c2c43142f787595e48280c40a1d0e"
dependencies = [
 "async-stream 0.3.3",
 "async-trait",
 "axum 0.6.18",
 "base64 0.21.2",
 "bytes",
 "flate2",
 "h2",
 "http",
 "http-body",
 "hyper",
 "hyper-timeout",
 "percent-encoding",
 "pin-project",
 "prost 0.12.1",
 "rustls 0.21.11",
 "rustls-pemfile",
 "tokio",
 "tokio-rustls 0.24.1",
 "tokio-stream",
 "tower",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tonic-build"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5bf5e9b9c0f7e0a7c027dcfaba7b2c60816c7049171f679d99ee2ff65d0de8c4"
dependencies = [
 "prettyplease 0.1.23",
 "proc-macro2",
 "prost-build 0.11.9",
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "tower"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fa9be0de6cf49e536ce1851f987bd21a43b771b09473c3549a6c853db37c1c"
dependencies = [
 "futures-core",
 "futures-util",
 "indexmap 1.6.2",
 "pin-project",
 "pin-project-lite",
 "rand 0.8.5",
 "slab",
 "tokio",
 "tokio-util",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower-http"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f873044bf02dd1e8239e9c1293ea39dad76dc594ec16185d0a1bf31d8dc8d858"
dependencies = [
 "bitflags 1.3.2",
 "bytes",
 "futures-core",
 "futures-util",
 "http",
 "http-body",
 "http-range-header",
 "pin-project-lite",
 "tower",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-http"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d1d42a9b3f3ec46ba828e8d376aec14592ea199f70a06a548587ecd1c4ab658"
dependencies = [
 "base64 0.20.0",
 "bitflags 1.3.2",
 "bytes",
 "futures-core",
 "futures-util",
 "http",
 "http-body",
 "http-range-header",
 "mime",
 "pin-project-lite",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower-layer"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c20c8dbed6283a09604c3e69b4b7eeb54e298b8a600d4d5ecb5ad39de609f1d0"

[[package]]
name = "tower-service"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6bc1c9ce2b5135ac7f93c72918fc37feb872bdc6a5533a8b85eb4b86bfdae52"

[[package]]
name = "tracing"
version = "0.1.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ce8c33a8d48bd45d624a6e523445fd21ec13d3653cd51f681abf67418f54eb8"
dependencies = [
 "cfg-if 1.0.0",
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f4f31f56159e98206da9efd823404b79b6ef3143b4a7ab76e67b1751b25a4ab"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "tracing-core"
version = "0.1.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0955b8137a1df6f1a2e9a37d8a6656291ff0297c1a97c24e0d8425fe2312f79a"
dependencies = [
 "once_cell",
]

[[package]]
name = "tracing-futures"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97d095ae15e245a057c8e8451bab9b3ee1e1f68e9ba2b4fbc18d0ac5237835f2"
dependencies = [
 "pin-project",
 "tracing",
]

[[package]]
name = "tracker"
version = "0.0.1"
dependencies = [
 "collections",
 "crossbeam-utils 0.8.21",
 "kvproto",
 "lazy_static",
 "parking_lot 0.12.0",
 "pin-project",
 "prometheus",
 "slab",
]

[[package]]
name = "treediff"
version = "4.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d127780145176e2b5d16611cc25a900150e86e9fd79d3bde6ff3a37359c9cb5"
dependencies = [
 "serde_json",
]

[[package]]
name = "try-lock"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e604eb7b43c06650e854be16a2a03155743d3752dd1c943f6829e26b7a36e382"

[[package]]
name = "tungstenite"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15fba1a6d6bb030745759a9a2a588bfe8490fc8b4751a277db3a0be1c9ebbf67"
dependencies = [
 "byteorder",
 "bytes",
 "data-encoding",
 "http",
 "httparse",
 "log",
 "rand 0.8.5",
 "sha1",
 "thiserror",
 "url",
 "utf-8",
]

[[package]]
name = "twoway"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "766345ed3891b291d01af307cd3ad2992a4261cb6c0c7e665cd3e01cf379dd24"
dependencies = [
 "memchr",
 "unchecked-index",
]

[[package]]
name = "twox-hash"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bfd5b7557925ce778ff9b9ef90e3ade34c524b5ff10e239c69a42d546d2af56"

[[package]]
name = "txn_types"
version = "0.1.0"
dependencies = [
 "bitflags 1.3.2",
 "byteorder",
 "codec",
 "collections",
 "error_code",
 "farmhash",
 "kvproto",
 "log_wrappers",
 "panic_hook",
 "rand 0.8.5",
 "slog",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "typenum"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "497961ef93d974e23eb6f433eb5fe1b7930b659f06d12dec6fc44a8f554c0bba"

[[package]]
name = "ucd-trie"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56dee185309b50d1f11bfedef0fe6d036842e3fb77413abef29f8f8d1c5d4c1c"

[[package]]
name = "unarray"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaea85b334db583fe3274d12b4cd1880032beab409c0d774be044d4480ab9a94"

[[package]]
name = "unchecked-index"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eeba86d422ce181a719445e51872fa30f1f7413b62becb52e95ec91aa262d85c"

[[package]]
name = "unicode-bidi"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49f2bd0c6468a8230e1db229cff8029217cf623c767ea5d60bfbd42729ea54d5"
dependencies = [
 "matches",
]

[[package]]
name = "unicode-ident"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84a22b9f218b40614adcb3f4ff08b703773ad44fa9423e4e0d346d5db86e4ebc"

[[package]]
name = "unicode-normalization"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5033c97c4262335cded6d6fc3e5c18ab755e1a3dc96376350f3d8e9f009ad956"
dependencies = [
 "tinyvec",
]

[[package]]
name = "unicode-segmentation"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6ccf251212114b54433ec949fd6a7841275f9ada20dddd2f29e9ceea4501493"

[[package]]
name = "unicode-width"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7007dbd421b92cc6e28410fe7362e2e0a2503394908f417b68ec8d1c364c4e20"

[[package]]
name = "unicode_categories"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39ec24b3121d976906ece63c9daad25b85969647682eee313cb5779fdd69e14e"

[[package]]
name = "unsafe-libyaml"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "673aac59facbab8a9007c7f6108d11f63b603f7cabff99fabf650fea5c32b861"

[[package]]
name = "untrusted"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a156c684c91ea7d62626509bce3cb4e1d9ed5c4d978f7b4352658f96a4c26b4a"

[[package]]
name = "untrusted"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecb6da28b8a351d773b68d5825ac39017e680750f980f3a1a85cd8dd28a47c1"

[[package]]
name = "url"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a507c383b2d33b5fc35d1861e77e6b383d158b2da5e14fe51b83dfedf6fd578c"
dependencies = [
 "form_urlencoded",
 "idna",
 "matches",
 "percent-encoding",
 "serde",
]

[[package]]
name = "usearch"
version = "2.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a34040838dcba06e4e21437f383975a0e3d6631ddbd54efd29490b444c693238"
dependencies = [
 "cxx",
 "cxx-build",
]

[[package]]
name = "utf-8"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09cc8ee72d2a9becf2f2febe0205bbed8fc6615b7cb429ad062dc7b7ddd036a9"

[[package]]
name = "utf8-ranges"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcfc827f90e53a02eaef5e535ee14266c1d569214c6aa70133a624d8a3164ba"

[[package]]
name = "utf8parse"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "936e4b492acfd135421d8dca4b1aa80a7bfc26e702ef3af710e0752684df5372"

[[package]]
name = "utime"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "055058552ca15c566082fc61da433ae678f78986a6f16957e33162d1b218792a"
dependencies = [
 "kernel32-sys",
 "libc 0.2.174",
 "winapi 0.2.8",
]

[[package]]
name = "uuid"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc5cf98d8186244414c848017f0e2676b3fcb46807f6668a97dfe67359a3c4b7"
dependencies = [
 "getrandom 0.2.16",
 "serde",
]

[[package]]
name = "uuid"
version = "1.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "422ee0de9031b5b948b97a8fc04e3aa35230001a722ddd27943e0be31564ce4c"
dependencies = [
 "getrandom 0.2.16",
 "serde",
]

[[package]]
name = "valgrind_request"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0fb139b14473e1350e34439c888e44c805f37b4293d17f87ea920a66a20a99a"

[[package]]
name = "vcpkg"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b00bca6106a5e23f3eee943593759b7fcddb00554332e856d990c893966879fb"

[[package]]
name = "vec_map"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05c78687fb1a80548ae3250346c3db86a80a7cdd77bda190189f2d0a0987c81a"

[[package]]
name = "version_check"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "914b1a6776c4c929a602fafd8bc742e06365d4bcbe48c30f9cca5824f70dc9dd"

[[package]]
name = "version_check"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49874b5167b65d7193b8aba1567f5c7d93d001cafc34600cee003eda787e483f"

[[package]]
name = "visible"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a044005fd5c0fc1ebd79c622e5606431c6b879a6a19acafb754be9926a2de73e"
dependencies = [
 "quote",
 "syn 1.0.107",
]

[[package]]
name = "vlog"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2266fcb904c50fb17fda4c9a751a1715629ecf8b21f4c9d78b4890fb71525d71"

[[package]]
name = "wait-timeout"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f200f5b12eb75f8c1ed65abd4b2db8a6e1b138a20de009dacee265a2498f3f6"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "walkdir"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "777182bc735b6424e1a57516d35ed72cb8019d85c8c9bf536dccb3445c1a2f7d"
dependencies = [
 "same-file",
 "winapi 0.3.9",
 "winapi-util",
]

[[package]]
name = "want"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ce8a968cb1cd110d136ff8b819a556d6fb6d919363c61534f6860c7eb172ba0"
dependencies = [
 "log",
 "try-lock",
]

[[package]]
name = "wasi"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b89c3ce4ce14bdc6fb6beaf9ec7928ca331de5df7e5ea278375642a2f478570d"

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasi"
version = "0.14.2+wasi-0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9683f9a5a998d873c0d21fcbe3c083009670149a8fab228644b8bd36b2c48cb3"
dependencies = [
 "wit-bindgen-rt",
]

[[package]]
name = "wasm-bindgen"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25f1af7423d8588a3d840681122e72e6a24ddbcb3f0ec385cac0d12d24256c06"
dependencies = [
 "cfg-if 1.0.0",
 "serde",
 "serde_json",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b21c0df030f5a177f3cba22e9bc4322695ec43e7257d865302900290bcdedca"
dependencies = [
 "bumpalo",
 "lazy_static",
 "log",
 "proc-macro2",
 "quote",
 "syn 1.0.107",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3de431a2910c86679c34283a33f66f4e4abd7e0aec27b6669060148872aadf94"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f4203d69e40a52ee523b2529a773d5ffc1dc0071801c87b3d270b471b80ed01"
dependencies = [
 "quote",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa8a30d46208db204854cadbb5d4baf5fcf8071ba5bf48190c3e59937962ebc"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.107",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d958d035c4438e28c70e4321a2911302f10135ce78a9c7834c0cab4123d06a2"

[[package]]
name = "web-sys"
version = "0.3.56"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c060b319f29dd25724f09a2ba1418f142f539b2be99fbf4d2d5a8f7330afb8eb"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "webpki"
version = "0.21.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8e38c0608262c46d4a56202ebabdeb094cef7e560ca7a226c6bf055188aa4ea"
dependencies = [
 "ring 0.16.20",
 "untrusted 0.7.1",
]

[[package]]
name = "webpki-roots"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aabe153544e473b775453675851ecc86863d2a81d786d741f6b76778f2a48940"
dependencies = [
 "webpki",
]

[[package]]
name = "whatlang"
version = "0.16.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "471d1c1645d361eb782a1650b1786a8fb58dd625e681a04c09f5ff7c8764a7b0"
dependencies = [
 "hashbrown 0.14.5",
 "once_cell",
]

[[package]]
name = "which"
version = "4.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a5a7e487e921cf220206864a94a89b6c6905bfc19f1057fa26a4cb360e5c1d2"
dependencies = [
 "either",
 "lazy_static",
 "libc 0.2.174",
]

[[package]]
name = "winapi"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "167dc9d6949a9b857f3451275e911c3f44255842c1f7a76f33c55103a909087a"

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-build"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d315eee3b34aca4797b2da6b13ed88266e6d612562a0c46390af8299fc699bc"

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70ec6ce85bb158151cae5e5c87f95a8e97d2c0c4b001223f33a334e3ce5de178"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "windows"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e686886bc078bc1b0b600cac0147aadb815089b6e4da64016cbd754b6342700f"
dependencies = [
 "windows-targets 0.48.1",
]

[[package]]
name = "windows-sys"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a3e1820f08b8513f676f7ab6c1f99ff312fb97b553d30ff4dd86f9f15728aa7"
dependencies = [
 "windows_aarch64_gnullvm 0.42.0",
 "windows_aarch64_msvc 0.42.0",
 "windows_i686_gnu 0.42.0",
 "windows_i686_msvc 0.42.0",
 "windows_x86_64_gnu 0.42.0",
 "windows_x86_64_gnullvm 0.42.0",
 "windows_x86_64_msvc 0.42.0",
]

[[package]]
name = "windows-sys"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677d2418bec65e3338edb076e806bc1ec15693c5d0104683f2efe857f61056a9"
dependencies = [
 "windows-targets 0.48.1",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.48.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05d4b17490f70499f20b9e791dcf6a299785ce8af4d709018206dc5b4953e95f"
dependencies = [
 "windows_aarch64_gnullvm 0.48.0",
 "windows_aarch64_msvc 0.48.0",
 "windows_i686_gnu 0.48.0",
 "windows_i686_msvc 0.48.0",
 "windows_x86_64_gnu 0.48.0",
 "windows_x86_64_gnullvm 0.48.0",
 "windows_x86_64_msvc 0.48.0",
]

[[package]]
name = "windows-targets"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973"
dependencies = [
 "windows_aarch64_gnullvm 0.52.6",
 "windows_aarch64_msvc 0.52.6",
 "windows_i686_gnu 0.52.6",
 "windows_i686_gnullvm",
 "windows_i686_msvc 0.52.6",
 "windows_x86_64_gnu 0.52.6",
 "windows_x86_64_gnullvm 0.52.6",
 "windows_x86_64_msvc 0.52.6",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41d2aa71f6f0cbe00ae5167d90ef3cfe66527d6f613ca78ac8024c3ccab9a19e"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91ae572e1b79dba883e0d315474df7305d12f569b400fcf90581b06062f7e1bc"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a4622180e7a0ec044bb555404c800bc9fd9ec262ec147edd5989ccd0c02cd3"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd0f252f5a35cac83d6311b2e795981f5ee6e67eb1f9a7f64eb4500fbc4dcdb4"

[[package]]
name = "windows_aarch64_msvc"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2ef27e0d7bdfcfc7b868b317c1d32c641a6fe4629c171b8928c7b08d98d7cf3"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ec2a7bb152e2252b53fa7803150007879548bc709c039df7627cabbd05d469"

[[package]]
name = "windows_i686_gnu"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbeae19f6716841636c28d695375df17562ca208b2b7d0dc47635a50ae6c5de7"

[[package]]
name = "windows_i686_gnu"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "622a1962a7db830d6fd0a69683c80a18fda201879f0f447f065a3b7467daa241"

[[package]]
name = "windows_i686_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e9b5ad5ab802e97eb8e295ac6720e509ee4c243f69d781394014ebfe8bbfa0b"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eee52d38c090b3caa76c563b86c3a4bd71ef1a819287c19d586d7334ae8ed66"

[[package]]
name = "windows_i686_msvc"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84c12f65daa39dd2babe6e442988fc329d6243fdce47d7d2d155b8d874862246"

[[package]]
name = "windows_i686_msvc"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4542c6e364ce21bf45d69fdd2a8e455fa38d316158cfd43b3ac1c5b1b19f8e00"

[[package]]
name = "windows_i686_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240948bc05c5e7c6dabba28bf89d89ffce3e303022809e73deaefe4f6ec56c66"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf7b1b21b5362cbc318f686150e5bcea75ecedc74dd157d874d754a2ca44b0ed"

[[package]]
name = "windows_x86_64_gnu"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca2b8a661f7628cbd23440e50b05d705db3686f894fc9580820623656af974b1"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "147a5c80aabfbf0c7d901cb5895d1de30ef2907eb21fbbab29ca94c5b08b1a78"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09d525d2ba30eeb3297665bd434a54297e4170c7f1a44cad4ef58095b4cd2028"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7896dbc1f41e08872e9d5e8f8baa8fdd2677f29468c4e156210174edc7f7b953"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d5b23dc417412679681396f2b49f3de8c1473deb516bd34410872eff51ed0d"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40009d85759725a34da6d89a94e63d7bdc50a862acf0dbc7c8e488f1edcb6f5"

[[package]]
name = "windows_x86_64_msvc"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a515f5799fe4961cb532f983ce2b23082366b898e52ffbce459c86f67c8378a"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589f6da84c646204747d1270a2a5661ea66ed1cced2631d546fdfb155959f9ec"

[[package]]
name = "winreg"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0120db82e8a1e0b9fb3345a539c478767c0048d842860994d96113d5b667bd69"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "wit-bindgen-rt"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f42320e61fe2cfd34354ecb597f86f413484a798ba44a8ca1165c58d42da6c1"
dependencies = [
 "bitflags 2.5.0",
]

[[package]]
name = "ws2_32-sys"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d59cefebd0c892fa2dd6de581e937301d8552cb44489cdff035c6187cb63fa5e"
dependencies = [
 "winapi 0.2.8",
 "winapi-build",
]

[[package]]
name = "x86"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "637be4bacc6c06570eb05a3ba513f81d63e52862ced82db542215dd48dbab1e5"
dependencies = [
 "bit_field",
 "bitflags 1.3.2",
 "csv",
 "phf 0.9.0",
 "phf_codegen 0.9.0",
 "raw-cpuid",
 "serde_json",
]

[[package]]
name = "xdg"
version = "2.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c4583db5cbd4c4c0303df2d15af80f0539db703fa1c68802d4cbbd2dd0f88f6"
dependencies = [
 "dirs",
]

[[package]]
name = "xml-rs"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "541b12c998c5b56aa2b4e6f18f03664eef9a4fd0a246a55594efae6cc2d964b5"

[[package]]
name = "xorf"
version = "0.8.0"
source = "git+https://github.com/youjiali1995/xorf?branch=0.8.0-custom-serde#96518a2160bdc8603a346aa4d94fdce22604abda"
dependencies = [
 "bytes",
 "libm",
 "rand 0.8.5",
 "serde",
]

[[package]]
name = "yatp"
version = "0.0.1"
source = "git+https://github.com/tikv/yatp.git?branch=master#39cb495953d40a7e846363c06090755c2eac65fa"
dependencies = [
 "crossbeam-deque",
 "dashmap 5.1.0",
 "fail 0.5.0",
 "lazy_static",
 "num_cpus",
 "parking_lot_core 0.9.10",
 "prometheus",
 "rand 0.8.5",
]

[[package]]
name = "zerocopy"
version = "0.7.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b9b4fd18abc82b8136838da5d50bae7bdea537c574d8dc1a34ed098d6c166f0"
dependencies = [
 "zerocopy-derive 0.7.35",
]

[[package]]
name = "zerocopy"
version = "0.8.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1039dd0d3c310cf05de012d8a39ff557cb0d23087fd44cad61df08fc31907a2f"
dependencies = [
 "zerocopy-derive 0.8.26",
]

[[package]]
name = "zerocopy-derive"
version = "0.7.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa4f8080344d4671fb4e831a13ad1e68092748387dfc4f55e356242fae12ce3e"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "zerocopy-derive"
version = "0.8.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ecf5b4cc5364572d7f4c329661bcc82724222973f2cab6f050a4e5c22f75181"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.104",
]

[[package]]
name = "zeroize"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a0956f1ba7c7909bfb66c2e9e4124ab6f6482560f6628b5aaeba39207c9aad9"

[[package]]
name = "zipf"
version = "6.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e12b8667a4fff63d236f8363be54392f93dbb13616be64a83e761a9319ab589"
dependencies = [
 "rand 0.7.3",
]

[[package]]
name = "zipf"
version = "7.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "835688a7a1b5d2dfaeb5b7e1b4cfb979e7095a70cd1c72fe083f4904ef3e995e"
dependencies = [
 "rand 0.8.5",
]

[[package]]
name = "zstd"
version = "0.11.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20cc960326ece64f010d2d2107537f26dc589a6573a316bd5b1dba685fa5fde4"
dependencies = [
 "zstd-safe 5.0.2+zstd.1.5.2",
]

[[package]]
name = "zstd"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e91ee311a569c327171651566e07972200e76fcfe2242a4fa446149a3881c08a"
dependencies = [
 "zstd-safe 7.2.1",
]

[[package]]
name = "zstd-safe"
version = "5.0.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d2a5585e04f9eea4b2a3d1eca508c4dee9592a89ef6f450c11719da0726f4db"
dependencies = [
 "libc 0.2.174",
 "zstd-sys",
]

[[package]]
name = "zstd-safe"
version = "7.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54a3ab4db68cea366acc5c897c7b4d4d1b8994a9cd6e6f841f8964566a419059"
dependencies = [
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.10+zstd.1.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c253a4914af5bafc8fa8c86ee400827e83cf6ec01195ec1f1ed8441bf00d65aa"
dependencies = [
 "cc",
 "pkg-config",
]

[[patch.unused]]
name = "lindera"
version = "0.43.1"
source = "git+https://github.com/breezewish/lindera?branch=v0.43.1-tokio-1.24#3e266dd69cae7ff1e894208cbfb9afa20d3e9965"
