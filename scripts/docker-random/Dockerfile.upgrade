ARG TIDB_VERSION=v7.5.0
ARG PD_VERSION=v8.5.0
ARG TIKV_VERSION
ARG TIFLASH_VERSION=v8.5.0

FROM hub-new.pingcap.net/keyspace/tidb:${TIDB_VERSION} as tidb
FROM hub-new.pingcap.net/keyspace/pd:${PD_VERSION} as pd
FROM hub-new.pingcap.net/keyspace/tikv-cse:${TIKV_VERSION} as tikv-server
FROM hub-new.pingcap.net/keyspace/tiflash:${TIFLASH_VERSION} as tiflash
FROM hub-new.pingcap.net/kvaas/go-tpc:small-size as go-tpc
FROM hub-new.pingcap.net/kvaas/minio:latest as minio
FROM hub-new.pingcap.net/kvaas/mc:latest as mc

FROM amazonlinux:2023.5.20241001.1
RUN dnf update -y && dnf install -y \
    procps-ng \
    binutils \
    jemalloc-devel \
    graphviz \
    ghostscript

COPY --from=tidb /tidb-server /tidb-server
COPY --from=pd /pd-server /pd-server
COPY --from=tikv-server /tikv-server /tikv-server
COPY --from=tikv-server /tikv-worker /tikv-worker
COPY --from=tiflash /tiflash /tiflash
COPY --from=go-tpc /go-tpc /go-tpc
COPY --from=minio /usr/bin/minio /usr/bin/minio
COPY --from=mc /usr/bin/mc /usr/bin/mc

ENV TIDB_BIN=/tidb-server
ENV PD_BIN=/pd-server
ENV TIKV_SERVER_BIN=/tikv-server
ENV TIKV_WORKER_BIN=/tikv-worker
ENV TIFLASH_BIN=/tiflash/tiflash
ENV TPC_BIN=/go-tpc
