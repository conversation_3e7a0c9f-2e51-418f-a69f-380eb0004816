ARG TIDB_VERSION=v7.5.0
ARG PD_VERSION=v8.5.0
ARG TIFLASH_VERSION=v7.5.0
ARG TICDC_VERSION=v7.5

FROM hub-new.pingcap.net/keyspace/tidb:${TIDB_VERSION} as tidb
FROM hub-new.pingcap.net/kvaas/tidb-next-gen:latest as tidb-next-gen
FROM hub-new.pingcap.net/keyspace/pd:${PD_VERSION} as pd
FROM hub-new.pingcap.net/keyspace/tiflash:${TIFLASH_VERSION} as tiflash
FROM hub-new.pingcap.net/kvaas/go-tpc:small-size as go-tpc
FROM hub-new.pingcap.net/kvaas/ticdc:${TICDC_VERSION} as ticdc

# To sync the minio image:
#   1. brew install skopeo (macOS)
#   2. skopeo copy --all docker://minio/minio:latest docker://hub-new.pingcap.net/kvaas/minio:latest
#   3. skopeo copy --all docker://minio/mc:latest docker://hub-new.pingcap.net/kvaas/mc:latest
FROM hub-new.pingcap.net/kvaas/minio:latest as minio
FROM hub-new.pingcap.net/kvaas/mc:latest as mc

FROM amazonlinux:2023.5.20241001.1
RUN dnf update -y && dnf install -y \
    procps-ng \
    binutils \
    jemalloc-devel \
    graphviz \
    ghostscript

COPY --from=tidb /tidb-server /tidb-server
COPY --from=tidb-next-gen /tidb-server /tidb-server-next-gen
COPY --from=pd /pd-server /pd-server
COPY --from=tiflash /tiflash /tiflash
COPY --from=go-tpc /go-tpc /go-tpc
COPY --from=ticdc /cdc /cdc
COPY --from=minio /usr/bin/minio /usr/bin/minio
COPY --from=mc /usr/bin/mc /usr/bin/mc

ENV TIDB_BIN=/tidb-server
ENV TIDB_NEXT_GEN_BIN=/tidb-server-next-gen
ENV PD_BIN=/pd-server
ENV TIFLASH_BIN=/tiflash/tiflash
ENV TPC_BIN=/go-tpc
ENV CDC_BIN=/cdc
