## Features Documentation

This directory contains documentation for various features such as **Native Backup and Restore**.

The purpose of these documents is to provide an **INDEX** of codes associated with these features. As sections of CSE codes are organized by modules (e.g. [`rfstore`](https://github.com/tidbcloud/cloud-storage-engine/tree/cloud-engine/components/rfstore)), it can be challenging to read codes of a feature as they would be distributed across different modules.

These documents aim to address this issue to some extent.

However, it should be noted that documents tend to become outdated as we, the developers, are not fond of writing them. Therefore, these documents only maintain an index of **CRITICAL** code path at the **MODULE** level. This enables us to minimize the effort required to maintain documentation over time while still ensuring that essential information is captured.

Finally, code reviewers should ensure to update these documents so the index remains current. This is the last measure to keep them up to date.
