// Copyright 2018 TiKV Project Authors. Licensed under Apache-2.0.

use kvproto::kvrpcpb::Context;
use test::Bencher;
use test_storage::SyncTestStorageBuilder;
use test_util::*;
use txn_types::{Key, Mutation};

/// In mvcc kv is not actually deleted, which may cause performance issue
/// when doing scan.
#[ignore]
#[bench]
fn bench_tombstone_scan(b: &mut Bencher) {
    let store = SyncTestStorageBuilder::default().build(0).unwrap();
    let mut ts_generator = 1..;

    let mut kvs = KvGenerator::new(100, 1000);

    for (k, v) in kvs.take(100_000) {
        let mut ts = ts_generator.next().unwrap();
        store
            .prewrite(
                Context::default(),
                vec![Mutation::make_put(Key::from_raw(&k), v)],
                k.clone(),
                ts,
            )
            .expect("");
        store
            .commit(
                Context::default(),
                vec![Key::from_raw(&k)],
                ts,
                ts_generator.next().unwrap(),
            )
            .expect("");

        ts = ts_generator.next().unwrap();
        store
            .prewrite(
                Context::default(),
                vec![Mutation::make_delete(Key::from_raw(&k))],
                k.clone(),
                ts,
            )
            .expect("");
        store
            .commit(
                Context::default(),
                vec![Key::from_raw(&k)],
                ts,
                ts_generator.next().unwrap(),
            )
            .expect("");
    }

    kvs = KvGenerator::new(100, 1000);
    b.iter(|| {
        let (k, _) = kvs.next().unwrap();
        assert!(
            store
                .scan(
                    Context::default(),
                    Key::from_raw(&k),
                    None,
                    1,
                    false,
                    ts_generator.next().unwrap(),
                )
                .unwrap()
                .is_empty()
        )
    })
}
